import 'package:changqing_health_app/config/dev_config.dart';
import 'package:changqing_health_app/config/enum/env_enum.dart';
import 'package:changqing_health_app/config/prod_config.dart';

/// author: Liusilong
/// date: 2024/8/26
/// desc: EnvConfig().env
//

class EnvConfig {
  EnvConfig._internal();

  static final EnvConfig _instance = EnvConfig._internal();

  factory EnvConfig() => _instance;

  Env? _env;
  Channel? _channel;
  String? appBaseUrl;
  String? buryingPointDomain;

  late String zhongtaiDomain;
  late String zhongtaiAppDomain;
  late String dvDomain;

  Env get env => _env!;

  String get channel => _channel!.value;

  void init({required Env env, required Channel channel}) {
    _env = env;
    _channel = channel;
    _init();
  }

  void _init() {
    appBaseUrl = switch (env) {
      Env.dev => DevConfig.appBaseUrl,
      Env.prod => ProdConfig.appBaseUrl,
    };
    zhongtaiDomain = switch (env) {
      Env.dev => DevConfig.zhongtaiDomain,
      Env.prod => ProdConfig.zhongtaiDomain,
    };
    dvDomain = switch (env) {
      Env.dev => DevConfig.dvDomain,
      Env.prod => ProdConfig.dvDomain,
    };
    zhongtaiAppDomain = switch (env) {
      Env.dev => DevConfig.zhongtaiAppDomain,
      Env.prod => ProdConfig.zhongtaiAppDomain,
    };
    buryingPointDomain = switch (env) {
      Env.dev => DevConfig.buryingPointDomain,
      Env.prod => ProdConfig.buryingPointDomain,
    };
  }
}
