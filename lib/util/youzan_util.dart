import 'dart:io';

import 'package:changqing_health_app/util/sentry_util.dart';
import 'package:cq_youzan_webview/cq_youzan_controller.dart';
import 'package:flutter/material.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2025-08-06
/// description: 有赞相关工具类

class YouzanUtil {
  YouzanUtil._();

  /// 初始化有赞SDK
  static Future<void> init() async {
    try {
      final appKey = Platform.isIOS ? 'be23e0ff7b7946c38eb7db2d918e1794' : 'ef147e6a829f41a9885101a670a7b73b';
      await CQYouZanController.initYZSdk(appKey: appKey, clientId: '3de7355c8465687071', iOSScheme: 'schemeios');
    } catch (e) {
      debugPrint("Youzan SDK initialization error: $e");
      SentryUtil.captureException("Youzan SDK initialization error: ${e.toString()}", stackTrace: StackTrace.current);
    }
  }

  /// 登录有赞
  static Future<void> login({
    required String userId,
    String avatar = '',
    String extra = '',
    String nickName = '',
    int gender = 0,
  }) async {
    try {
      await CQYouZanController.login(userId: userId.toString());
    } catch (e) {
      debugPrint("Youzan login error: $e");
      SentryUtil.captureException("Youzan login error: ${e.toString()}", stackTrace: StackTrace.current);
    }
  }

  /// 退出有赞登录状态
  static Future<void> logout() async {
    try {
      await CQYouZanController.logout();
    } catch (e) {
      debugPrint("Youzan logout error: $e");
      SentryUtil.captureException("Youzan logout error: ${e.toString()}", stackTrace: StackTrace.current);
    }
  }
}
