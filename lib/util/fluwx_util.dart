import 'package:fluwx/fluwx.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2025-07-21
/// desc: 微信工具类

/// 微信工具类
class FluwxUtil {
  FluwxUtil._();

  final Fluwx _fluwx = Fluwx();

  static final FluwxUtil _instance = FluwxUtil._();

  factory FluwxUtil() {
    return _instance;
  }

  /// 打开小程序
  /// [username] 小程序username, gh_xxxxx
  /// [path] 小程序path
  /// [miniProgramType] 小程序类型
  Future<void> openMiniProgram(String username, {String? path, WXMiniProgramType? miniProgramType}) async {
    await _fluwx.open(target: MiniProgram(username: username, path: path, miniProgramType: miniProgramType ?? WXMiniProgramType.release));
  }
}
