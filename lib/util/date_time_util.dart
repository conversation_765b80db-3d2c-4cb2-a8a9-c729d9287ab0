import 'package:intl/intl.dart';

class DateTimeUtil {
  DateTimeUtil._();

  /// 格式化时间戳为MM/DD HH:mm
  static String formatToMMDDHHmm(int time) {
    return DateFormat('MM/dd HH:mm').format(DateTime.fromMillisecondsSinceEpoch(time));
  }


  /// 格式化时间戳为MM/DD HH:mm
  static String format({String format = 'MM/dd HH:mm', String? time}) {
    return DateFormat(format).format(DateTime.parse(time!));
  }
}
