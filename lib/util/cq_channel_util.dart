import 'dart:io';

import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class CQChannelUtil {
  late MethodChannel _channel;

  static final CQChannelUtil _instance = CQChannelUtil._();

  factory CQChannelUtil() => _instance;

  CQChannelUtil._() {
    _channel = MethodChannel("cq_channel");
  }

  /// 获取 android 端 oaid
  Future<String> getOAID() async {
    if (!Platform.isAndroid) return "";
    try {
      return await _channel.invokeMethod('getOAID');
    }catch(_){
      return '';
    }
  }

  /// 获取 Android IMEI
  Future<String> getIMEI() async {
    if (!Platform.isAndroid) return "";
    if (await Permission.phone.isDenied == true) return "";
    // 2024-08-26 由于 Android 13 获取IMEI被禁止
    // return await _channel.invokeMethod('getIMEI');
    return "";
  }

  /// 获取UserAgent
  Future<String> getUA() async {
    if (!Platform.isAndroid) return "";
    try {
      return await _channel.invokeMethod('getUA');
    }catch(_){
      return '';
    }
  }

  Future<String> getAndroidId() async {
    if (!Platform.isAndroid) return "";
    try {
      return await _channel.invokeMethod('getAndroidId');
    }catch(_){
      return '';
    }
  }

  /// 获取iOS 端的 IDFV，缓存到钥匙串，不会改变
  Future<String> getCustomIDFV() async {
    if (!Platform.isIOS) return "";
    try {
      return await _channel.invokeMethod('getIDFV');
    } catch (_) {
      return '';
    }
  }
}
