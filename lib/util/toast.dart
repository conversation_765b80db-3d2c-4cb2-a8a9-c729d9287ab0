import 'package:bot_toast/bot_toast.dart';
import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/widget/cq_loading_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

// Toast.showLoading();

// Toast.showLoading(message: '加载中');

// Toast.showText('网络未连接，请检查网络');

// Toast.show(message: '加载中');

// Toast.show(
//   icon: Icon(
//     IconData(0xe612, fontFamily: 'iconfont'),
//     color: Colors.white,
//     size: 72.sp,
//   ),
// );

// Toast.show(icon: 0xe612);

// Toast.show(icon: Image.asset('assets/images/icon_loading.png'));

// Toast.show(
//   message: '加载中',
//   icon: Image.asset('assets/images/icon_loading.png'),
// );

// Future.delayed(Duration(seconds: 1), () {
//   toast();
// });

class Toast {
  /// 显示一个自定义的toast
  /// [message] 提示文案
  /// [icon] toast icon
  /// [crossPage] 跳转路由是否继续显示toast
  /// [clickClose] 点击屏幕关闭toast
  /// [onlyOne] 是否只显示一个toast，通过group key区分
  /// [allowClick] 显示toast时时是否可以触发屏幕事件
  /// [duration] 持续时间
  static show(
    String message, {
    icon,
    bool crossPage = true,
    bool clickClose = false,
    bool onlyOne = true,
    bool allowClick = false,
    Duration duration = const Duration(seconds: 1),
    double coefficient = 1.0,
  }) {
    coefficient = globalContext!.portrait ? 1.0 : 0.5;
    return BotToast.showEnhancedWidget(
      onlyOne: onlyOne,
      clickClose: clickClose,
      crossPage: crossPage,
      allowClick: allowClick,
      duration: duration,
      toastBuilder: (cancelFunc) {
        return _ToastWrapper(
          message: message,
          icon: icon,
          coefficient: coefficient,
        );
      },
    );
  }

  /// 显示一个提示文案
  /// [message] 提示文案
  /// [crossPage] 跳转路由是否继续显示toast
  /// [clickClose] 点击屏幕关闭toast
  /// [onlyOne] 是否只显示一个toast，通过group key区分
  /// [allowClick] 显示toast时时是否可以触发屏幕事件
  /// [duration] 持续时间
  static showMessage(
    String message, {
    bool crossPage = true,
    bool clickClose = false,
    bool onlyOne = true,
    bool allowClick = true,
    Duration duration = const Duration(seconds: 2),
    double coefficient = 1.0,
  }) {
    coefficient = globalContext!.portrait ? 1.0 : 0.5;
    return BotToast.showEnhancedWidget(
      onlyOne: onlyOne,
      clickClose: clickClose,
      crossPage: crossPage,
      allowClick: allowClick,
      duration: duration,
      toastBuilder: (cancelFunc) {
        return _ToastWrapper(
          message: message,
          coefficient: coefficient,
        );
      },
    );
  }

  /// 显示一个loading
  /// [message] 提示文案
  /// [crossPage] 跳转路由继续显示toast
  /// [clickClose] 点击屏幕关闭toast
  /// [onlyOne] 是否只显示一个toast，通过group key区分
  /// [allowClick] 显示toast时时是否可以触发屏幕事件
  /// [duration] 持续时间
  // static CancelFunc _showLoading(
  //   String message, {
  //   bool crossPage = true,
  //   bool clickClose = false,
  //   bool onlyOne = true,
  //   bool allowClick = false,
  //   Duration? duration,
  //   double coefficient = 1.0,
  // }) {
  //   return BotToast.showEnhancedWidget(
  //     onlyOne: onlyOne,
  //     clickClose: clickClose,
  //     crossPage: crossPage,
  //     allowClick: allowClick,
  //     duration: duration,
  //     toastBuilder: (cancelFunc) {
  //       return _ToastWrapper(
  //         message: message,
  //         icon: RotationLoading(
  //           coefficient: coefficient,
  //         ),
  //         coefficient: coefficient,
  //       );
  //     },
  //   );
  // }

  static CancelFunc? _cancelFunc;
  static int _loadingCount = 0;
  static showLoading() {
    _loadingCount++;
    if (_cancelFunc != null) return;
    _cancelFunc = BotToast.showEnhancedWidget(
      clickClose: false,
      allowClick: false,
      toastBuilder: (cancelFunc) {
        return Center(
          child: CQLoadingWidget(),
        );
      },
    );
  }

  static closeLoading() {
    _loadingCount--;
    if (_loadingCount <= 0) {
      _cancelFunc?.call();
      _cancelFunc = null;
      _loadingCount = 0;
    }
  }

  static resetLoading() {
    _cancelFunc?.call();
    _cancelFunc = null;
    _loadingCount = 0;
  }

  /// 显示一个透明的loading
  static showTransparentLoading(String message,
      {bool crossPage = true, bool clickClose = false, bool onlyOne = true, bool allowClick = false, Duration? duration, double coefficient = 1.0}) {
    return BotToast.showEnhancedWidget(
      onlyOne: onlyOne,
      clickClose: clickClose,
      crossPage: crossPage,
      allowClick: allowClick,
      duration: duration,
      toastBuilder: (cancelFunc) {
        return Container(
          color: Colors.transparent,
        );
      },
    );
  }
}

/// toast样式
/// [message] 文案
/// [icon] 图标
class _ToastWrapper extends StatelessWidget {
  const _ToastWrapper({required this.message, this.icon, this.coefficient = 1.0});

  final String message;

  final icon;

  /// 横屏状态下，需要乘以一个系数，不然UI太大
  final double coefficient;

  BorderRadius _currentBorderRadius() {
    if (icon == null) {
      return BorderRadius.circular(12.w * coefficient);
    }
    return BorderRadius.circular(24.w * coefficient);
  }

  EdgeInsets _currentPadding() {
    if (icon == null) {
      return EdgeInsets.symmetric(horizontal: 24.w * coefficient, vertical: 16.w * coefficient);
    }
    return EdgeInsets.symmetric(horizontal: 46.w * coefficient, vertical: 36.w * coefficient);
  }

  Widget _onlyMessage() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          message,
          style: TextStyle(
            color: Colors.white,
            fontSize: 28.sp * coefficient,
          ),
          textAlign: TextAlign.center,
          maxLines: 4,
          overflow: TextOverflow.ellipsis,
        )
      ],
    );
  }

  Widget _onlyIcon() {
    Widget _icon = const Offstage();
    if (icon is int) {
      _icon = Icon(
        IconData(icon, fontFamily: 'iconfont'),
        color: Colors.white,
        size: 72.sp * coefficient,
      );
    } else if (icon is Widget) {
      _icon = icon;
    }
    return Container(
      width: 72.w * coefficient,
      height: 72.w * coefficient,
      child: _icon,
    );
  }

  Widget _currentContent() {
    if (icon == null) {
      return _onlyMessage();
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        icon,
        Padding(
          padding: EdgeInsets.only(top: 16.w * coefficient),
          child: Text(
            message,
            style: TextStyle(
              color: Colors.white,
              fontSize: 28.sp * coefficient,
            ),
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // ScreenAdaper.init(context);
    return Center(
      child: Container(
        constraints: BoxConstraints(
          minWidth: 100.w * coefficient,
          minHeight: 72.w * coefficient,
          maxWidth: 496.w * coefficient,
          maxHeight: 500.w * coefficient,
        ),
        padding: _currentPadding(),
        decoration: BoxDecoration(
          color: Color.fromRGBO(0, 0, 0, 0.8),
          borderRadius: _currentBorderRadius(),
        ),
        child: _currentContent(),
      ),
    );
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty('icon', icon));
  }
}

/// loading
class RotationLoading extends StatefulWidget {
  /// 横屏状态下，需要乘以一个系数，不然UI太大
  final double coefficient;

  const RotationLoading({super.key, required this.coefficient});

  @override
  State createState() => _RotationLoadingState();
}

class _RotationLoadingState extends State<RotationLoading> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animationController.reset();
        _animationController.forward();
      }
    });
    _animationController.forward();
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return RotationTransition(
      turns: _animationController,
      child: Image.asset(
        'assets/images/icon_loading.png',
        width: 72.w * widget.coefficient,
        height: 72.w * widget.coefficient,
      ),
    );
  }
}
