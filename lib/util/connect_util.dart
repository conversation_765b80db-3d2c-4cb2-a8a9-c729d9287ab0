import 'package:connectivity_plus/connectivity_plus.dart';

// author: <PERSON><PERSON><PERSON>
// date: 2024/8/27
// desc:

class ConnectUtil {
  ConnectUtil._();

  static final ConnectUtil _instance = ConnectUtil._();

  factory ConnectUtil() => _instance;

  /// 获取当前网络状态
  Future<bool> get hasNetwork async {
    List<ConnectivityResult> resultList = await (Connectivity().checkConnectivity());
    return !resultList.contains(ConnectivityResult.none);
  }
}
