import 'dart:io';
import 'package:path_provider/path_provider.dart';

/// 移动文件到Documents目录
Future<List<String>> moveFileToDocuments(List<String> originPaths) async {
  try {
    final documentsDir = await getApplicationDocumentsDirectory();
    final newFilePaths = <String>[];
    for (final originPath in originPaths) {
      final file = File(originPath);
      final fileName = file.uri.pathSegments.last;
      final newDir = Directory('${documentsDir.path}/chat');
      if (!newDir.existsSync()) {
        await newDir.create(recursive: true);
      }
      final newFilePath = '${newDir.path}/$fileName';
      await file.rename(newFilePath);
      newFilePaths.add(newFilePath);
    }
    return newFilePaths;
  } catch (e) {
    print(e);
    return originPaths;
  }
}

/// 获取文件名和 MIME 类型
(String fileName, String mimeType) getFileNameAndMimeType(String path) {
  final fileName = path.split('/').last;
  final mimeType = switch (path.split('.').last) {
    'jpg' || 'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'pdf' => 'application/pdf',
    _ => 'image/png',
  };
  return (fileName, mimeType);
}
