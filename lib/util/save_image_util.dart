import 'dart:io';
import 'package:changqing_health_app/util/toast.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gal/gal.dart';
import 'package:permission_handler/permission_handler.dart';

class SaveImageUtil {
  static Future<void> saveImage(String url) async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt <= 32) {
        await [Permission.storage].request();
      } else {
        await [Permission.photos].request();
      }
    }

    try {
      if (url.startsWith('http')) {
        var response = await Dio().get(url, options: Options(responseType: ResponseType.bytes));
        await Gal.putImageBytes(
          Uint8List.fromList(response.data),
          name: DateTime.now().millisecondsSinceEpoch.toString(),
        );
      } else {
        await Gal.putImage(File(url).path);
      }
      Toast.show('保存成功');
    } catch (e) {
      debugPrint('saveImage error: $e');
      Toast.show('保存失败，请检查权限');
    }
  }
}
