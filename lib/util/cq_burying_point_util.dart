import 'dart:convert';
import 'dart:io';

import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/provider/router/router_provider.dart';
import 'package:changqing_health_app/util/cq_channel_util.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/widgets.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppBuryingPointUtil {
  AppBuryingPointUtil._();

  static String _pointUrl = '';

  static const String SDK_VERSION = '1.0.1';

  // 连接超时时间
  static const int CONNECT_TIMEOUT = 30000;

  // 接收超时时间
  static const int RECEIVE_TIMEOUT = 30000;

  /// 基础字段
  static final Map _commonPointData = {};

  /// 扩展字段
  static final Map _commonAlternateData = {};

  /// 请求器
  static Dio? _dio;

  /// 注意，要在 同意策略后 进行  初始化
  static Future init({
    required String domain,
    required String serviceName,
  }) async {
    _pointUrl = '$domain/?wm-data-name=wmdata&form_type=json';
    _commonPointData['services_name'] = serviceName;
    await _initCommonPointData();
    await _initCommonAlternateData();
    await _initDio();
  }

  static click({required String modulePart, String? moduleOri, String? prePage, String? currentPage, Map? alternate}) {
    _log(event: 'click', modulePart: modulePart, moduleOri: moduleOri, prePage: prePage, currentPage: currentPage, alternate: alternate);
  }

  static show({required String modulePart, String? moduleOri, String? prePage, String? currentPage, Map? alternate}) {
    _log(event: 'show', modulePart: modulePart, moduleOri: moduleOri, prePage: prePage, currentPage: currentPage, alternate: alternate);
  }
  static heartbeat({required String modulePart, String? moduleOri, String? prePage, String? currentPage, Map? alternate}) {
    _log(event: 'heartbeat', modulePart: modulePart, moduleOri: moduleOri, prePage: prePage, currentPage: currentPage, alternate: alternate);
  }



  static  _log({
    required String event,
    required String modulePart,
    String? moduleOri,
    String? prePage,
    String? currentPage,
    Map? alternate,
  })  {
    try {
      Map data = {};
      data.addAll(_commonPointData);
      data['event'] = event;
      data['modulePart'] = modulePart;
      data['moduleOri'] = moduleOri;
      data['prePage'] = prePage ?? previousRouteName;
      data['currentPage'] = currentPage ?? currentRouteName;
      Map postAlternate = Map.from(_commonAlternateData);
      if (alternate != null) {
        postAlternate.addAll(alternate);
      }

      data['alternate'] = postAlternate;

      final mid = SpUtil().get(spAppMid) ?? '';
      data['mid'] = mid;

      debugPrint('本次埋点信息 = ${jsonEncode(data)}');

      return;
      return _dio?.post(_pointUrl, data: data).then((value) {
        debugPrint('埋点上报 成功 value = $value');
        return ;
      }).catchError((e){
        debugPrint('埋点上报 失败  = $e');
        return ;
      });
    } catch (e) {
      debugPrint('本次埋点 失败 = $e');
    }
  }

  /// 初始化 基础字段
  static Future _initCommonPointData() async {
    String deviceNo = await _getDeviceNo();
    String os = Platform.isIOS ? 'iOS' : 'Android';
    String touristId = await _getTouristId();
    final packageInfo = await PackageInfo.fromPlatform();
    String version = packageInfo.version;
    String sdkVersion = SDK_VERSION;
    _commonPointData["device_no"] = deviceNo;
    _commonPointData["os"] = os;
    _commonPointData["tourist_id"] = touristId;
    _commonPointData["version"] = version;
    _commonPointData["sdkVersion"] = sdkVersion;
  }

  /// 初始化 公共扩展字段
  static Future _initCommonAlternateData() async {
    String oaid = await CQChannelUtil().getOAID();
    String imei = await CQChannelUtil().getIMEI();
    String ua = await CQChannelUtil().getUA();
    String idfa = ''; // iOS想要获取广告标识符，需要弹框允许，否则获取到的都是000000000

    String brand = '';
    String model = '';
    if (Platform.isIOS) {
      IosDeviceInfo info = await DeviceInfoPlugin().iosInfo;
      brand = info.model;
      model = info.utsname.machine;
    } else {
      AndroidDeviceInfo info = await DeviceInfoPlugin().androidInfo;
      brand = info.board;
      model = info.model;
    }

    _commonAlternateData['appChannel'] = EnvConfig().channel;
    _commonAlternateData['brand'] = brand;
    _commonAlternateData['model'] = model;
    _commonAlternateData['idfa'] = idfa;
    _commonAlternateData['oaid'] = oaid;
    _commonAlternateData['imei'] = imei;
    _commonAlternateData['ua'] = ua;
  }

  static Future<String> _getDeviceNo() async {
    String deviceNo = '';
    if (Platform.isIOS) {
      IosDeviceInfo info = await DeviceInfoPlugin().iosInfo;
      deviceNo = info.systemVersion;
    } else {
      AndroidDeviceInfo info = await DeviceInfoPlugin().androidInfo;
      deviceNo = info.version.release;
    }
    return deviceNo;
  }

  static Future<String> _getTouristId() async {
    if (Platform.isIOS) {
      return CQChannelUtil().getCustomIDFV();
    } else {
      return CQChannelUtil().getAndroidId();
    }
  }

  static Future _initDio() async {
    BaseOptions baseOptions = BaseOptions(
      connectTimeout: Duration(milliseconds: CONNECT_TIMEOUT),
      receiveTimeout: Duration(milliseconds: RECEIVE_TIMEOUT),
      headers: {},
    );

    Dio dio = Dio(baseOptions);

    String? proxy = SpUtil().get(spAppNetworkProxy);
    if (proxy != null && proxy.isNotEmpty) {
      (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        HttpClient client = HttpClient();
        // config the http client
        client.findProxy = (uri) {
          return HttpClient.findProxyFromEnvironment(uri, environment: {
            "http_proxy": proxy,
            "https_proxy": proxy,
            "HTTP_PROXY": proxy,
            "HTTPS_PROXY": proxy,
          });
        };
        client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
        return client;
      };
    }

    return dio;
  }
}

/*
{
"mid"
"event"
"module_part"
"module_ori"
"pre_page"
"current_page"
}


 */

/*
{device_no: 15, os: Android, tourist_id: 3d1b3fb9d6c70c88, version: 1.9.44, sdkVersion: 1.0.1, event: show, module_part: 119_app_duration, module_ori: 基于程序, mid: 1620395868340050, pre_page: root, current_page: /home,
alternate: {"appChannel":"1000","brand":"samsung","model":"SM-G9910","idfa":"","oaid":"aa37f8bed1b08a92cb64e2515c581940d7676d00a95dce474cc106ddc659482c",
"imei":"","ua":"Dalvik/2.1.0 (Linux; U; Android 15; SM-G9910 Build/AP3A.240905.015.A2)","startTime":1754387665281,"endTime":1754387666359}, services_name: app}

 */
