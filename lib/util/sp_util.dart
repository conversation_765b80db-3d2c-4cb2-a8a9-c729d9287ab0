import 'package:shared_preferences/shared_preferences.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2024/3/13
/// desc:
//
class SpUtil {
  SharedPreferences? _sp;

  SpUtil._();

  static final SpUtil _instance = SpUtil._();

  factory SpUtil() => _instance;

  /// 初始化
  Future<void> init() async {
    if (_sp != null) return;
    _sp = await SharedPreferences.getInstance();
  }

  /// put
  /// [key] key
  /// [value] value
  Future<bool> set<T>(String key, T value) async {
    if (_sp == null) return false;
    if (value is String) {
      return await _sp!.setString(key, value);
    } else if (value is double) {
      return await _sp!.setDouble(key, value);
    } else if (value is int) {
      return await _sp!.setInt(key, value);
    } else if (value is bool) {
      return await _sp!.setBool(key, value);
    }
    return false;
  }

  /// get
  /// [key] key
  T? get<T>(String key) {
    if (_sp == null) return null;
    if (_sp!.get(key) == null) return null;
    return _sp!.get(key) as T;
  }

  /// remove
  /// [key] key
  Future<bool> remove(String key) async {
    if (_sp == null) return false;
    if (_sp!.containsKey(key)) {
      return _sp!.remove(key);
    }
    return false;
  }

  /// setStringList
  /// [key] key
  /// [value] value
  Future<bool> setStringList(String key, List<String> value) async {
    if (_sp == null) return false;
    return await _sp!.setStringList(key, value);
  }

  /// getStringList
  /// [key] key
  List<String>? getStringList(String key) {
    if (_sp == null) return null;
    return _sp!.getStringList(key);
  } 
}
