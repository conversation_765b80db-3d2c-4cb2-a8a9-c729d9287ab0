import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/config/enum/env_enum.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class SentryUtil {
  SentryUtil._();

  /// 根据环境获取 dsn
  static String get dsn => switch (EnvConfig().env) {
        Env.dev => 'https://<EMAIL>//12',
        Env.prod => 'https://<EMAIL>//13',
      };

  /// 手动捕获异常
  static void captureException(dynamic throwable, {dynamic stackTrace, Map<String, dynamic>? scopeContext}) {
    try {
      Sentry.captureException(throwable, stackTrace: stackTrace, withScope: (scope) {
        final mid = SpUtil().get(spAppMid) ?? 'unknown';
        final path = GoRouter.of(globalContext!).state.uri.path;
        final contextMap = {
          'mid': mid,
          'path': path,
        };
        if (scopeContext != null) {
          contextMap.addAll(scopeContext);
        }
        scope.setContexts('app_context', contextMap);
        scope.setTag('mid', mid);
        scope.setTag('path', path);
        scope.setUser(SentryUser(id: mid, data: {"path": path}));
      }).then((value) {
        debugPrint('SentryUtil.captureException success: $value');
      });
    } catch (e) {
      debugPrint('SentryUtil.captureException error: $e');
    }
  }

  /// 上报时设置全局额外的参数
  static void setGlobalContexts(Map<String, dynamic> params) {
    Sentry.configureScope((scope) {
      params.forEach((key, value) {
        scope.setContexts(key, value);
      });
    });
  }
}
