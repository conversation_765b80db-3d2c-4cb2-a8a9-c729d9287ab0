import 'dart:io';

import 'package:changqing_health_app/app/app.dart';
import 'package:changqing_health_app/config/enum/env_enum.dart';

/// 用于 Jenkins 构建时，通过环境变量获取环境信息
Future<void> main() async {
  final (env, channel) = _getEnvAndChannel();
  await startApp(env: env, channel: Platform.isAndroid ? channel : Channel.base);
}

/// 获取环境信息
(Env env, Channel channel) _getEnvAndChannel() {
  const envStr = String.fromEnvironment('ENV');
  const channelStr = String.fromEnvironment('CHANNEL');
  Env env;

  if (envStr == Env.dev.name) {
    env = Env.dev;
  } else {
    env = Env.prod;
  }
  Channel channel;
  if (channelStr == Channel.huawei.name) {
    channel = Channel.huawei;
  } else if (channelStr == Channel.xiaomi.name) {
    channel = Channel.xiaomi;
  } else if (channelStr == Channel.oppo.name) {
    channel = Channel.oppo;
  } else if (channelStr == Channel.vivo.name) {
    channel = Channel.vivo;
  } else if (channelStr == Channel.rongyao.name) {
    channel = Channel.rongyao;
  } else {
    channel = Channel.base;
  }
  return (env, channel);
}
