import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/service/http/response_model.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

class ApiClient {
  ApiClient._();

  static Dio? _dio;

  static Dio get dio {
    if (_dio == null) {
      BaseOptions options = BaseOptions(
        // baseUrl: baseUrl,
        connectTimeout: const Duration(milliseconds: 3000 * 10),
        receiveTimeout: const Duration(milliseconds: 3000 * 10),
      );
      _dio = Dio(options);
    }
    return _dio!;
  }

  static init({required String baseUrl, List<Interceptor>? interceptors}) {
    if (interceptors != null && interceptors.isNotEmpty) {
      dio.interceptors.addAll(interceptors);
    }
    /// 设置代理信息
    _handleProxy();
    dio.options.baseUrl = baseUrl;
  }

  static _handleProxy(){
    String? proxy = SpUtil().get(spAppNetworkProxy);
    if (proxy == null || proxy.isEmpty) return;
    (dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
      HttpClient client = HttpClient();
      // config the http client
      client.findProxy = (uri) {
        return HttpClient.findProxyFromEnvironment(uri, environment: {
          "http_proxy": proxy,
          "https_proxy": proxy,
          "HTTP_PROXY": proxy,
          "HTTPS_PROXY": proxy,
        });
      };
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      return client;
    };
  }

  static Future<dynamic> get(String path, {Map<String, dynamic>? param, CancelToken? cancelToken}) async {
    try {
      Response response = await dio.get(path, queryParameters: param, cancelToken: cancelToken);
      if (response.data == null) return null;
      ResponseModel model = ResponseModel.fromJson(response.data);
      return model;
    } catch (e) {
      print('Error during GET request: $e');
      return null;
    }
  }

  static Future<dynamic> post(String path, dynamic data) async {
    try {
      Response response = await dio.post(path, data: data);
      try {
        ResponseModel model = ResponseModel.fromJson(response.data);
        return model;
      } catch (parseError) {
        // 如果解析失败，返回原始响应数据
        return response.data;
      }
    } catch (e) {
      print('Error during POST request: $e');
      return null; // 网络请求失败时返回 null
    }
  }

  static Future<dynamic> delete(String path, {Map<String, dynamic>? param}) async {
    try {
      Response response = await dio.delete(path, queryParameters: param);
      ResponseModel model = ResponseModel.fromJson(response.data);
      return model;
    } catch (e) {
      print('Error during DELETE request: $e');
      return null;
    }
  }

  static Future<dynamic> put(String path, {Map<String, dynamic>? data}) async {
    try {
      Response response = await dio.put(path, data: data);
      ResponseModel model = ResponseModel.fromJson(response.data);
      return model;
    } catch (e) {
      print('Error during DELETE request: $e');
      return null;
    }
  }

  static Stream<String> getStream(String url, Map<String, dynamic> data) async* {
    final response = await dio.post(
      url,
      data: data,
      options: Options(
        responseType: ResponseType.stream,
        // headers: {
        //   'Accept': 'text/event-stream',
        //   'Cache-Control': 'no-cache',
        // },
      ),
      onReceiveProgress: (count, total) {
        print('count: $count, total: $total');
      },
      onSendProgress: (count, total) {
        print('count: $count, total: $total');
      },
    );
    await for (final chunk in response.data!.stream) {
      final decodedChunk = utf8.decode(chunk);
      yield decodedChunk;
    }
  }

  static Future<void> testAI() async {
    Response<ResponseBody> rs = await Dio().post<ResponseBody>(
      "http://test-cqailab.keyuanjiankang.com/agent/chat",
      data: {"user_input": "早餐吃什么"},
      options: Options(
        headers: {
          "Accept": "text/event-stream",
          "Cache-Control": "no-cache",
        },
        responseType: ResponseType.stream,
      ),
    );
    StreamTransformer<Uint8List, List<int>> unit8Transformer = StreamTransformer.fromHandlers(
      handleData: (data, sink) {
        sink.add(List<int>.from(data));
      },
    );
    rs.data?.stream.transform(unit8Transformer).transform(const Utf8Decoder()).transform(const LineSplitter()).listen((event) {
      print(event);
    });
  }

  Stream<String> askAI(String question) async* {
    try {
      final response = await dio.post<ResponseBody>(
        'https://your-backend-api.com/chat',
        data: {'question': question},
        options: Options(responseType: ResponseType.stream),
      );

      // 确保响应数据存在
      if (response.data == null) {
        throw Exception("Empty response");
      }

      // 解析流式数据
      final stream = response.data!.stream.transform(
        StreamTransformer.fromHandlers(
          handleData: (data, sink) {
            sink.add(utf8.decode(data));
          },
        ),
      );
      await for (var chunk in stream) {
        yield chunk as String; // 逐步返回流式数据
      }
    } catch (e) {
      yield "Error: $e";
    }
  }
}
