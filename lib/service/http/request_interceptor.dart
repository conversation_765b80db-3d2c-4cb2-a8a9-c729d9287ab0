import 'dart:io';

import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/config/enum/env_enum.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/service/http/sign/aes_helper.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// author: Liu<PERSON>long
/// date: 2024/3/13
/// desc:
//

Map<String, dynamic>? cqjkRequestInterceptorHeader;

class RequestInterceptor extends InterceptorsWrapper {
  final Ref ref;

  RequestInterceptor({required this.ref});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final deviceVersion = ref.read(deviceInfoProvider).value?.version;
    final uid = SpUtil().get(spAppUid) ?? '';
    final token = SpUtil().get(spAppToken) ?? '';
    final time = (DateTime.now().millisecondsSinceEpoch).toString();
    final nonce = AESHelper.generateRandomString(10);
    String s = "t=$time&nonce=$nonce";
    if (uid != '') {
      s = '$s&uid=$uid';
    }
    final signature = AESHelper.md5EncryptText(s);
    final header = {
      "Authorization": token,
      "X-App": cqjkAppKey,
      "X-Uid": uid,
      "X-Token": token,
      "X-Timestamp": time,
      "X-Nonce": nonce,
      "X-Version": deviceVersion,
      "X-Device": Platform.isAndroid ? "android" : "ios",
      "X-Signature": signature,
      "token": token, // 切到旭景服务需要此token
      "channel": EnvConfig().channel,
    };
    cqjkRequestInterceptorHeader = header;
    // print('onRequest');
    options.headers.addAll(header);
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    print('onResponse = ${response.data}');
    final data = response.data;
    if (data is Map) {
      final code = data['code'];
      // 正式99999
      if (code == 100401 || code == 99999) {
        // 99999是旭景的服务
        // 退出登录
        Toast.show('登录状态已过期，请重新登录');
        ref.read(loginProvider.notifier).logout();
        globalContext?.go(ref.read(getLoginRouteProvider));
      }
    }
    handler.resolve(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    handler.next(err);
  }
}
