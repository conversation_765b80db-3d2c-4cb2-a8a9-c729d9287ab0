import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'dart:convert';

class AESHelper {
  static final _iv = encrypt.IV.fromLength(0); // ECB 模式不使用 IV

  // 默认 key（你可以替换为任意 16 字节的字符串）
  static const _defaultKey = "6de3a9d7b98219161db331799bbf139a";

  static String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final rand = Random.secure();
    return List.generate(length, (index) => chars[rand.nextInt(chars.length)]).join();
  }

  static String md5EncryptText(String plainText) {
    // 4. 获取 base64 编码后的密文
    final encryptedBase64 = encryptText(plainText);

    // 5. 对加密结果做 MD5
    final result = md5.convert(utf8.encode(encryptedBase64)).toString();

    return result;
  }

  static String encryptText(String plainText) {
    final key = encrypt.Key.fromUtf8(_defaultKey);
    // 2. 初始化 AES ECB 加密器
    final encrypter = encrypt.Encrypter(
      encrypt.AES(key, mode: encrypt.AESMode.ecb, padding: 'PKCS7'),
    );

    // 3. 加密（ECB 模式不需要 IV）
    final encrypted = encrypter.encrypt(plainText, iv: encrypt.IV.fromLength(0));

    // 4. 获取 base64 编码后的密文
    final encryptedBase64 = encrypted.base64;

    return encryptedBase64;
  }

  static String decryptText(String base64CipherText) {
    final key = encrypt.Key.fromUtf8(_defaultKey);
    final encrypter = encrypt.Encrypter(
      encrypt.AES(key, mode: encrypt.AESMode.ecb, padding: 'PKCS7'),
    );
    final encryptedBytes = base64.decode(base64CipherText);
    final encryptedData = encrypt.Encrypted(encryptedBytes);
    final decrypted = encrypter.decrypt(encryptedData, iv: _iv);
    return decrypted;
  }
}
