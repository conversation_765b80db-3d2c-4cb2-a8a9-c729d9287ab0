/// author: <PERSON><PERSON><PERSON>
/// date: 2024/8/26
/// desc:
//
// 是否同意隐私政策
const String spPrivacyAgreement = "spPrivacyAgreement";

// 首次安装App时是否同意隐私弹框
const String spFirstInstallAgreement = "spFirstInstallAgreement";

// 服务热线
String servicePhone = "010-86485365";

// 用户协议
const String userAgreementUrl = "https://wm-front-common-http2.weimiaocaishang.com/official-website/agreements/cqjk/user-agreement.html";
// 隐私政策
const String privacyAgreementUrl = "https://wm-front-common-http2.weimiaocaishang.com/official-website/agreements/cqjk/privacy-agreement.html";

/// 长轻营养⾷疗App自身的业务线key
const String cqjkAppKey = "cqjk-app";
const String cqjkAppId = "178";

/// 体验营
const int classPackageExperienceCamp = 5;

/// 陪伴营
const int classPackageCompanionCamp = 4;

// 长青健康服务
const String cqServiceUrl = "https://www.cqslim.com/community";

const String spAppNetworkProxy = 'spAppNetworkProxy';

const String spAppToken = 'appToken';

const String spAppUid = 'appUid';

const String spAppMid = 'appMid';

const String spAppJwt = 'appJwt';

/// 腾讯云播放器license
const String licenceUrl = "https://license.vod2.myqcloud.com/license/v2/1301695313_1/v_cube.license";
const String licenceKey = "bfba1c9f65176b8e14a5a117b0939aaf";
