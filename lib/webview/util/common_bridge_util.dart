import 'dart:convert';

import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/h5liveroom/util/h5_scheme_manager.dart';
import 'package:changqing_health_app/module/login/api/login_api.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart';
import 'package:changqing_health_app/module/user/route/user_route.dart';
import 'package:changqing_health_app/util/save_image_util.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/webview/constant/js_bridge_method_name.dart';
import 'package:changqing_health_app/webview/model/cq_java_script_message_model.dart';
import 'package:changqing_health_app/widget/h5_pay_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:webview_flutter/webview_flutter.dart';

class CommonBridgeUtil {
  final WidgetRef ref;
  WebViewController? _controller;

  CommonBridgeUtil(this.ref);

  bool onMessageReceived({required WebViewController controller, required CQJavaScriptMessageModel messageModel}) {
    _controller = controller;
    String methodName = messageModel.methodName;
    Map<dynamic, dynamic>? params = messageModel.params;
    switch (methodName) {
      case JsBridgeMethodName.getToken:
        _getToken(methodName);
        return true;
      case JsBridgeMethodName.forceRefreshToken:
        _forceRefreshToken(methodName);
        return true;
      case JsBridgeMethodName.savePic:
        _savePic(params!);
        return true;
      case JsBridgeMethodName.openPage:
        _openPage(params!);
        return true;
      case JsBridgeMethodName.payWithApp:
        _payWithApp(methodName, params!);
        return true;
    }
    return false;
  }

  void _getToken(String methodName) {
    final isLogin = ref.read(loginProvider);
    if (isLogin) {
      final token = SpUtil().get(spAppToken);

      CQCallJSMethodUtil.callJsMethod(controller: _controller, methodName: methodName, params: {
        "token": token,
        "appKey": cqjkAppKey,
        "appId": cqjkAppId,
      });
    } else {
      globalContext!.push(ref.read(getLoginRouteProvider));
    }
  }

  /// 强制刷新token
  void _forceRefreshToken(String methodName) async {
    bool success = await LoginApi.refreshToken();
    if (success) {
      final token = SpUtil().get(spAppToken);

      CQCallJSMethodUtil.callJsMethod(controller: _controller, methodName: methodName, params: {
        "token": token,
        "appKey": cqjkAppKey,
        "appId": cqjkAppId,
      });
      return;
    }
    // 静默刷新失败，弹出登录弹框
    globalContext!.push(ref.read(getLoginRouteProvider));
  }

  /// 保存图片
  void _savePic(Map<dynamic, dynamic> params) async {
    SaveImageUtil.saveImage(params['url']);
  }

  /// 打开新的页面加载直播 url
  void _openPage(Map<dynamic, dynamic> params) async {
    String url = params['url'];
    String? title = params['title'] ?? '';
    if (url.contains('youzan.com')) {
      YouzanMallRoute(url: url, title: title).push(globalContext!);
    } else {
      H5SchemeManager.openLiveUrlOnNewPage(url, title: title);
    }
  }

  /// 页面可见性发生变化
  void didChangeAppLifecycleState(AppLifecycleState state) {
    int? hidden;
    if (state == AppLifecycleState.resumed) {
      // 可见
      hidden = 0;
    } else if (state == AppLifecycleState.inactive) {
      // 不可见
      hidden = 1;
    }
    if (hidden != null) {
      CQCallJSMethodUtil.callJsMethod(controller: _controller, methodName: JsBridgeMethodName.visibilityChange, params: {"hidden": hidden});
    }
  }

  void _payWithApp(String methodName, Map<dynamic, dynamic> params) {
    debugPrint('payWithApp: $params');

    if (params['type'] == 'AH5') {
      CQPayWebViewUtil.aliPay(formData: params['formData']);
    } else {
      String formData = params['formData'];
      String payUrl = jsonDecode(formData)['h5_url'];
      String domain = params['domain'];
      CQPayWebViewUtil.weChatPay(url: payUrl, domain: domain);
    }
  }
}

class CQCallJSMethodUtil {
  static void callJsMethod({required WebViewController? controller, required String methodName, required Map params}) {
    String javaScript = 'window.WeiMiaoJSBridge.callEvent("$methodName", ${jsonEncode(params)})';
    debugPrint('JsMethod -- App to H5 javaScript = $javaScript');
    controller?.runJavaScript(javaScript);
  }
}
