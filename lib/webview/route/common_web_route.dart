
import 'package:changqing_health_app/webview/page/common_web_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

part 'common_web_route.g.dart';

const String commonWebRoutePath = '/common_web';


@TypedGoRoute<CommonWebRoute>(path: commonWebRoutePath)
/// H5直播路由
class CommonWebRoute extends GoRouteData {
  final String url;
  final String title;
  const CommonWebRoute({required this.url, required this.title});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return CommonWebPage(url: url, title: title);
  }
}

