import 'package:changqing_health_app/webview/base/webview_container.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TabWebViewWidget extends StatefulWidget {
  final String url;

  const TabWebViewWidget({super.key, required this.url});

  @override
  State<TabWebViewWidget> createState() => _TabWebViewWidgetState();
}

class _TabWebViewWidgetState extends State<TabWebViewWidget> {
  late WebViewController _controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: _bodyWidget(),
    );
  }

  Widget _bodyWidget() {
    return WebviewContainer(
      url: widget.url,
      onCreated: (controller) async {
        _controller = controller;
        _controller.loadRequest(Uri.parse(widget.url));
      },
      needReloadCallBack: () {
        _controller.reload();
      },
    );
  }
}
