import 'package:changqing_health_app/util/toast.dart';
import 'package:changqing_health_app/webview/base/webview_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';

// author: <PERSON><PERSON><PERSON>
// date: 2024/8/29
// desc:

class CommonWebPage extends ConsumerStatefulWidget {
  final String url;
  final String title;

  const CommonWebPage({required this.url, required this.title, super.key});

  @override
  ConsumerState<CommonWebPage> createState() => _CommonWebPageState();
}

class _CommonWebPageState extends ConsumerState<CommonWebPage> {
  late WebViewController _controller;

  bool _canGoBack = false;

  String? _title;

  @override
  void initState() {
    super.initState();
    _title = widget.title;
  }

  @override
  void dispose() {
    debugPrint('CommonWebPage --- dispose');
    Toast.closeLoading();
    super.dispose();
  }

  void _changeCloseBtn() async {
    _canGoBack = await _controller.canGoBack();
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        if (await _controller.canGoBack()) {
          _controller.goBack();
          _changeCloseBtn();
        } else {
          if (context.mounted) {
            Navigator.pop(context);
          }
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          title: Text(_title ?? '', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Colors.black)),
          actions: _canGoBack
              ? [
                  IconButton(
                    icon: const Icon(Icons.close), // 返回图标
                    onPressed: () {
                      Navigator.pop(context); // 返回上一页
                    },
                  ),
                ]
              : null,
        ),
        body: _bodyWidget(),
      ),
    );
  }

  Widget _bodyWidget() {
    return WebviewContainer(
      url: widget.url,
      onCreated: (controller) async {
        _controller = controller;
        if (controller.platform is AndroidWebViewController) {
          await (controller.platform as AndroidWebViewController).setMediaPlaybackRequiresUserGesture(false);
        }
        _controller.loadRequest(Uri.parse(widget.url));
      },
      onPageStarted: (_) {
        Toast.showLoading();
      },
      onPageFinished: (_) async {
        Toast.resetLoading();
        String? title = await _controller.getTitle();
        // if(title?.isNotEmpty ?? false) {
        //   _title = title!;
        //   setState(() {});
        // }
      },
      onProgress: (int progress) {
        if (progress >= 98) {
          Toast.resetLoading();
        }
      },
      onUrlChange: (_) {
        _changeCloseBtn();
      },
      needReloadCallBack: () {
        _controller.reload();
      },
    );
  }
}
