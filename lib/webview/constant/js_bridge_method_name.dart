// author: <PERSON><PERSON><PERSON>
// date: 2024/8/29
// desc: js 通信方法

class JsBridgeMethodName {
  JsBridgeMethodName._();

  static const String getToken = 'get_token';

  /// 利用app支付
  static const String payWithApp = "payWithApp";

  /// 保存图片
  static const String savePic = "savePic";

  /// 强制调用app登录，刷新token
  static const String forceRefreshToken = "forceRefreshToken";

  /// 从后台重新进入前台后，调用一次 H5
  /// 1不可见  0可见
  /// {"hidden" : 1}
  static const String visibilityChange = "visibilitychange";

  /// 通知App跳转页面后打开某个直播Url
  static const String openPage = "openPage";
}
