import 'dart:async';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/webview/model/cq_java_script_message_model.dart';
import 'package:changqing_health_app/webview/util/common_bridge_util.dart';
import 'package:changqing_health_app/widget/app_lifecycle_widget.dart';
import 'package:changqing_health_app/widget/h5_pay_widget.dart';
import 'package:changqing_health_app/widget/route_aware_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:webview_flutter_wkwebview/src/common/webkit_constants.dart';

// author: Liusilong
// date: 2024/8/28
// desc:

class WebviewContainer extends ConsumerStatefulWidget {
  final String url;
  final JavaScriptMode? javascriptMode;
  final WebViewCookie? cookie;
  final Function(WebViewController controller)? onCreated;
  final Function(String url)? onPageStarted;
  final Function(String url)? onPageFinished;
  final FutureOr<NavigationDecision> Function(NavigationRequest request)? onNavigationRequest;
  final Function(int progress)? onProgress;
  final Function(WebResourceError error)? onWebResourceError;
  final Function(UrlChange change)? onUrlChange;
  final Function(CQJavaScriptMessageModel)? onMessageReceived;
  final Function()? needReloadCallBack;

  const WebviewContainer({
    super.key,
    required this.url,
    this.javascriptMode,
    this.cookie,
    this.onCreated,
    this.onPageStarted,
    this.onPageFinished,
    this.onNavigationRequest,
    this.onProgress,
    this.onUrlChange,
    this.onWebResourceError,
    this.onMessageReceived,
    this.needReloadCallBack,
  });

  @override
  ConsumerState<WebviewContainer> createState() => _WebviewContainerState();
}

class _WebviewContainerState extends ConsumerState<WebviewContainer> {
  late final WebViewController _controller;
  late final NavigationDelegate _navigationDelegate;
  late final CommonBridgeUtil _bridgeUtil;
  bool _needReload = false;

  @override
  void initState() {
    super.initState();
    _initNavigationDelegate();
    _initWebController();
    WidgetsBinding.instance.addPostFrameCallback(_afterLayout);
  }

  void _afterLayout(_) {
    if (widget.onCreated != null) {
      widget.onCreated!(_controller);
    }
  }

  void _initWebController() async {
    // 设置 cookie
    if (widget.cookie != null) {
      WebViewCookieManager().setCookie(widget.cookie!);
    }
    // final params = WebKitWebViewControllerCreationParams(
    //   allowsInlineMediaPlayback: true, // ✅ 关键参数：允许内联播放
    //   mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{}, // 可选：自动播放支持
    // );
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    // 初始化 WebViewController
    _controller = WebViewController.fromPlatformCreationParams(params)
      ..setJavaScriptMode(widget.javascriptMode ?? JavaScriptMode.unrestricted)
      ..setNavigationDelegate(_navigationDelegate);

    _controller.clearCache();

    _bridgeUtil = CommonBridgeUtil(ref);

    String? userAgent = await _controller.getUserAgent() ?? '';
    _controller.setUserAgent('$userAgent;appAgent=cqjkapp_${ref.read(appVersionProviderProvider).value?.version};');
    _controller.loadRequest(Uri.parse(widget.url));
    // 设置 js 消息通道
    _controller.addJavaScriptChannel("WeiMiao", onMessageReceived: (msg) {
      // 公共的 js 消息处理
      CQJavaScriptMessageModel model = CQJavaScriptMessageModel.fromJSMessage(msg.message);
      debugPrint('JsMethod -- H5 to App name = ${model.methodName} param = ${model.params}');
      if (_bridgeUtil.onMessageReceived(controller: _controller, messageModel: model)) {
        return;
      }
      // 自定义的 js 消息处理
      if (widget.onMessageReceived != null) {
        widget.onMessageReceived!(model);
      }
    });
  }

  void _initNavigationDelegate() {
    _navigationDelegate = NavigationDelegate(
      onPageStarted: (String url) {
        if (CQPaySchemeHandler.canHandle(url)) {
          CQPaySchemeHandler.handleUrl(url);
          return;
        }
        print('Page started loading: $url');
        if (widget.onPageStarted != null) {
          widget.onPageStarted!(url);
        }
      },
      onPageFinished: (String url) async {
        print('Page finished loading: $url');
        if (widget.onPageFinished != null) {
          widget.onPageFinished!(url);
        }
        String? userAgent = await _controller.getUserAgent();
        debugPrint('userAgent = $userAgent');
      },
      onWebResourceError: (WebResourceError error) {

        debugPrint('onWebResourceError = ${error.errorCode}  msg = ${error.description}  type = ${error.errorType}');
        if (error.errorCode == WKErrorCode.webContentProcessTerminated) {
          _needReload = true;
          widget.needReloadCallBack?.call();
        }
        if (widget.onWebResourceError != null) {
          widget.onWebResourceError!(error);
        }
      },
      onNavigationRequest: (NavigationRequest request) {
        if (widget.onNavigationRequest != null) {
          return widget.onNavigationRequest!(request);
        } else {
          return NavigationDecision.navigate;
        }
      },
      onProgress: (int progress) {
        if (widget.onProgress != null) {
          widget.onProgress!(progress);
        }
        print('WebView is loading (progress : $progress%)');
      },
      onUrlChange: (UrlChange change) {
        print('onUrlChange: ${change.url}');
        if (widget.onUrlChange != null) {
          widget.onUrlChange!(change);
        }
      },
    );
  }

  void _checkNeedReload() {
    if (_needReload) {
      _controller.reload();
      _needReload = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RouteAwareWidget(
      didPopNext: _checkNeedReload,
      child: AppLifecycleWidget(
        didChangeAppLifecycleState: (state) => _bridgeUtil.didChangeAppLifecycleState(state),
        child: WebViewWidget(
          controller: _controller,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.removeJavaScriptChannel("WeiMiao");
    super.dispose();
  }
}
