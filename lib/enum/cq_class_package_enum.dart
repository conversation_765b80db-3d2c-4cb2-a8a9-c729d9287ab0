
//1修复营双师课  2修复营加餐课 3陪伴营课 4深体营课	5，体验营
// enum ClassPackageEnum {
//   // 1修复营双师课
//   repairCampDoubleTeacher(1),
//   // 2修复营加餐课
//   repairCampAddMeal(2),
//   // 3深体营课
//   deepBodyCamp(3),
//   // 4陪伴营课
//   companionCamp(4),
//   // 5，体验营
//   experienceCamp(5);
//
//   final int value;
//
//   const ClassPackageEnum(this.value);
//
//   // 根据 int 值获取枚举
//   static ClassPackageEnum fromValue(int value) {
//     return ClassPackageEnum.values.firstWhere(
//           (status) => status.value == value,
//       orElse: () => ClassPackageEnum.repairCampDoubleTeacher, // 默认值
//     );
//   }
// }