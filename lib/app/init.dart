import 'dart:io';
import 'dart:ui';

import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/util/sentry_util.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/util/youzan_util.dart';
import 'package:cq_youzan_webview/cq_youzan_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluwx/fluwx.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

/// 初始化 app
Future<void> initApp() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  // 设置 Android 沉浸式状态栏
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent, // 透明状态栏
    statusBarIconBrightness: Brightness.dark, // 状态栏图标颜色（黑色）
    systemNavigationBarColor: Colors.white, // 导航栏颜色
    systemNavigationBarIconBrightness: Brightness.dark, // 导航栏图标颜色
  ));
  // 强制应用程序保持竖屏方向
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
  // 设置异常捕获
  FlutterError.onError = (FlutterErrorDetails details) {
    SentryUtil.captureException(details.exception, stackTrace: details.stack);
  };
  // 设置未捕获异常捕获
  PlatformDispatcher.instance.onError = (error, stack) {
    SentryUtil.captureException(error, stackTrace: stack);
    return true;
  };
  try {
    await SpUtil().init();
    final isAgreeAgreement = SpUtil().get(spFirstInstallAgreement) ?? false;
    // 同意了用户协议，则初始化第三方 SDK
    if (isAgreeAgreement) {
      initThirdPartySdk();
    }
  } catch (e) {
    print(e);
  }
}

/// 初始化 app 中第三方 SDK
Future<void> initThirdPartySdk() async {
  try {
    YouzanUtil.init();
    await _initSentry();
    await _initFluwx();
    await _initTxVideoLicence();
    print("app init success");
  } catch (e) {
    print("app init error: $e");
  }
}

/// 初始化 sentry
Future<void> _initSentry() async {
  SentryWidgetsFlutterBinding.ensureInitialized();
  await SentryFlutter.init((options) {
    options.dsn = SentryUtil.dsn;
    options.sendDefaultPii = true;

    // 明确启用原生集成（默认已启用，但显式设置更清晰）
    options.enableAutoNativeBreadcrumbs = true;
    options.enableNativeCrashHandling = true;

    // Android 特定配置
    options.enableAutoSessionTracking = false;
    options.enableAppHangTracking = false; // ANR 检测

    // 调试配置
    options.debug = false;
    options.environment = EnvConfig().env.name;
    options.tracesSampleRate = 1.0;

    // 性能监控
    options.attachScreenshot = false;
    options.attachViewHierarchy = false;
    // 设置 beforeSend 过滤 DioException
    options.beforeSend = (event, hint) {
      if (event.throwable != null && event.throwable.runtimeType.toString() == 'DioException') {
        debugPrint("Sentry BeforeSend: 过滤DioException");
        return null; // 返回null表示不上报
      }
      print('🛰️ beforeSend event: ${event.contexts}');
      return event;
    };
  });
}

/// 重置系统 UI 样式
void resetSystemUIStyle() {
  SystemChrome.setSystemUIOverlayStyle(
    SystemUiOverlayStyle(
      statusBarColor: const Color(0xffF2F5F9), // 状态栏背景色
      statusBarIconBrightness: Brightness.dark, // 状态栏图标颜色

      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark, // 图标亮色
    ),
  );
}

/// 初始化 fluwx
Future<void> _initFluwx() async {
  bool success = await Fluwx().registerApi(appId: "wx92e1f9209d62afe1", universalLink: "https://ai-apk.keyuanjiankang.com/healthapp/");
  if (!success) {
    print("fluwx registerApi failed");
  } else {
    print("fluwx registerApi success");
  }
}

/// 设置腾讯视频播放器 licence
Future<void> _initTxVideoLicence() async {
  return TXVideoPlayerController.setLicence(licenceUrl: licenceUrl, licenceKey: licenceKey);
}
