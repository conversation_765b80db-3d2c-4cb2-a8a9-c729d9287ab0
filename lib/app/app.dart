import 'dart:async';

import 'package:bot_toast/bot_toast.dart';
import 'package:changqing_health_app/app/init.dart';
import 'package:changqing_health_app/config/enum/env_enum.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/provider/router/router_provider.dart';
import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:changqing_health_app/util/sentry_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: Liusilong
/// date: 2024-08-26
/// desc:
//

/// 启动 app
/// [env] 当前app运行的环境
/// [channel] 当前app运行的渠道
Future<void> startApp({required Env env, required Channel channel}) async {
  runZonedGuarded(() async {
    EnvConfig().init(env: env, channel: channel);
    await initApp();
    _runApp();
  }, (error, stackTrace) {
    SentryUtil.captureException(error, stackTrace: stackTrace);
  });
}

void _runApp() {
  runApp(const ProviderScope(child: MyApp()));
  // SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark.copyWith(statusBarColor: Colors.transparent));
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  @override
  void initState() {
    super.initState();
    ApiClient.init(
      baseUrl: EnvConfig().appBaseUrl!,
      interceptors: [ref.read(requestInterceptorProvider), LogInterceptor(requestBody: true, responseBody: true)],
    );
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1624),
      builder: (_, child) {
        return MediaQuery(
          // 设置字体大小不随系统字体大小变化
          data: MediaQuery.of(context).copyWith(textScaler: TextScaler.linear(1.0)),
          child: MaterialApp.router(
            debugShowCheckedModeBanner: false,
            title: '长轻营养⾷疗',
            routerConfig: ref.read(routerProvider),
            theme: ThemeData(
              useMaterial3: true,
              primaryColor: Colors.white,
              scaffoldBackgroundColor: const Color(0xffF2F5F9),
              colorScheme: const ColorScheme.light().copyWith(
                // primary: Colors.cyan,
                primary: const Color(0xff3477f0),
                surfaceTint: const Color(0xffF2F5F9), // 顶部导航栏在列表上移时，顶部导航栏颜色
              ),
              appBarTheme: const AppBarTheme(
                elevation: 10.5,
                backgroundColor: Colors.white,
                centerTitle: true,
                titleTextStyle: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
            ),
            // navigatorKey: navigatorKey,
            // initialRoute: Routes.root.name,
            // onGenerateRoute: FluroRouter.appRouter.generator,
            builder: BotToastInit(),
            locale: const Locale('zh', 'CN'),
            supportedLocales: const [
              Locale('zh', 'CN'),
              Locale('en', 'US'),
            ],
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
          ),
        );
      },
    );
  }
}

void _onError(Object error, StackTrace stackTrace) {
  print('error:$error');
}
