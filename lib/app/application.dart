import 'package:flutter/cupertino.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2024/2/28
/// desc:
//

// final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();
final routeObserver = RouteTracker();

class RouteTracker extends RouteObserver<PageRoute<dynamic>> {
  String? currentRouteName;
  String? previousRouteName;

  @override
  void didPush(Route route, Route? previousRoute) {
    if (route is PageRoute) {
      previousRouteName = currentRouteName;
      currentRouteName = route.settings.name;
    }
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    if (previousRoute is PageRoute) {
      currentRouteName = previousRoute.settings.name;
    }
    super.didPop(route, previousRoute);
  }
}

GlobalKey<NavigatorState> navigatorKey = GlobalKey();

BuildContext? get globalContext => navigatorKey.currentContext;
