
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppEmptyRefreshTipsWidget extends StatelessWidget {
  final VoidCallback? onRefresh;

  const AppEmptyRefreshTipsWidget({super.key, this.onRefresh});

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('数据跑丢了~，刷新试试吧!', style: TextStyle(color: Colors.black26, fontSize: 30.sp)),
        20.gap,
        GestureDetector(
          onTap: (){
            onRefresh?.call();
          },
          child: Container(
            height: 70.w,
            width: 140.w,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.w),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF9EE7A3), // 稍浓一点，草绿渐现
                  Color(0xFF80D68A), // 柔和草绿色，依然清淡不刺眼
                  Color(0xFF9EE7A3), // 稍浓一点，草绿渐现
                ],
              ),
            ),
            child: Text(
              '刷新',
              style: TextStyle(color: Colors.white, fontSize: 30.sp, fontWeight: FontWeight.bold),
            ),
          ),
        )
      ],
    ));
  }
}
