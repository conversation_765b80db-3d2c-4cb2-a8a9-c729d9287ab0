import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2024/3/19
/// desc:
//

class ModalBottomSheetWidget extends StatefulWidget {
  final String? title;
  final Widget child;
  final double? minHeight;
  final double? maxHeight;
  final Color? color;

  const ModalBottomSheetWidget({
    required this.child,
    this.title,
    this.minHeight,
    this.maxHeight,
    this.color,
    super.key,
  });

  @override
  State<ModalBottomSheetWidget> createState() => _ModalBottomSheetWidgetState();
}

class _ModalBottomSheetWidgetState extends State<ModalBottomSheetWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  Widget _buildContent() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: widget.maxHeight ?? context.sh,
        minHeight: widget.minHeight ?? 0,
      ),
      width: context.sw,
      decoration: BoxDecoration(
        color: widget.color ?? Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(40.w),
          topRight: Radius.circular(40.w),
        ),
      ),
      child: widget.child,
    );
  }
}
