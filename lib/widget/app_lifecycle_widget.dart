import 'package:flutter/material.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2/6/21
/// desc: APP 生命周期监听
//

class AppLifecycleWidget extends StatefulWidget {
  final Function(AppLifecycleState) didChangeAppLifecycleState;
  final Widget child;
  final Key? key;

  AppLifecycleWidget({
    required this.didChangeAppLifecycleState,
    required this.child,
    this.key,
  }) : super(key: key);

  @override
  _AppLifecycleWidgetState createState() => _AppLifecycleWidgetState();
}

class _AppLifecycleWidgetState extends State<AppLifecycleWidget> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    widget.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
