import 'dart:io';

import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:changqing_health_app/widget/app_lifecycle_widget.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// 目前直播间有赞外链无法调起支付宝或者微信
/// 所以在webview中，开始加载url时做scheme的判断
class CQPaySchemeHandler {
  /// 判断是否是我们支持的自定义协议
  static bool canHandle(String url) {
    if(!Platform.isIOS) return false;
    return url.startsWith('weixin://') || url.startsWith('alipay://') || url.startsWith('alipays://');
  }

  static Future handleUrl(String url) async {
    if (url.startsWith('weixin://')) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
    if (url.startsWith('alipay://') || url.startsWith('alipays://')) {
      String requestUrl = url;
      String? aliPayScheme = appBackendConfig['replaceAliPayScheme'];
      if (aliPayScheme != null && aliPayScheme.isNotEmpty) {
        requestUrl = requestUrl.replaceAll('fromAppUrlScheme%22%3A%22$aliPayScheme', 'fromAppUrlScheme%22%3A%22cqappalipays');
      }
      await launchUrl(Uri.parse(requestUrl), mode: LaunchMode.externalApplication);
    }
  }
}

/// H5支付
class H5PayController {
  Function(String url, String domain)? _weChatPay;
  Function(String formData)? _aliPay;
  VoidCallback? _appResumed;

  /// 微信支付
  /// [url] 支付url
  /// [domain] 唤起微信app的Referer
  /// [appResumed] app回到前台事件
  void weChatPay({required String url, required String domain, VoidCallback? appResumed}) {
    _appResumed = appResumed;
    _weChatPay?.call(url, domain);
  }

  /// 支付宝支付
  /// [formData] 唤起支付宝支付的支付数据
  /// [appResumed] app回到前台事件
  void aliPay({required String formData, VoidCallback? willOpenAliApp, VoidCallback? appResumed}) {
    _appResumed = appResumed;
    _aliPay?.call(formData);
  }

  void _dispose() {
    _aliPay = null;
    _weChatPay = null;
    _appResumed = null;
  }
}

class CQPayWebViewUtil {
  static OverlayEntry? _overlayEntry;
  static H5PayController? _payController;

  /// 微信支付
  /// [url] 支付url
  /// [domain] 唤起微信app的Referer
  /// [appResumed] app回到前台事件
  static void weChatPay({required String url, required String domain, VoidCallback? appResumed}) {
    _payController?._dispose();
    _payController = H5PayController();
    _payController!._appResumed = appResumed;
    _initOverlayEntry(initCallBack: () {
      _payController!.weChatPay(url: url, domain: domain, appResumed: appResumed);
    });
  }

  /// 支付宝支付
  /// [formData] 唤起支付宝支付的支付数据
  /// [appResumed] app回到前台事件
  static void aliPay({required String formData, VoidCallback? willOpenAliApp, VoidCallback? appResumed}) {
    _payController?._dispose();
    _payController = H5PayController();
    _payController!._appResumed = appResumed;
    _initOverlayEntry(initCallBack: () {
      _payController!.aliPay(formData: formData);
    });
  }

  static void _initOverlayEntry({required VoidCallback? initCallBack}) {
    _removeOverlayEntry();
    _overlayEntry = OverlayEntry(
      builder: (_) => H5PayWidget(controller: _payController!, initCallBack: initCallBack),
    );
    navigatorKey.currentState?.overlay?.insert(_overlayEntry!);
  }

  static void _removeOverlayEntry() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class H5PayWidget extends StatefulWidget {
  final H5PayController controller;
  final VoidCallback? initCallBack;

  const H5PayWidget({super.key, required this.controller, required this.initCallBack});

  @override
  State<H5PayWidget> createState() => _H5PayWidgetState();
}

class _H5PayWidgetState extends State<H5PayWidget> {
  final WebViewController _controller = WebViewController();

  @override
  void dispose() {
    _loadBlank();
    super.dispose();
  }

  @override
  void initState() {
    widget.controller._weChatPay = _weChatPay;
    widget.controller._aliPay = _aliPay;
    _controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    _controller.setNavigationDelegate(NavigationDelegate(
      onNavigationRequest: (request) {
        debugPrint('onNavigationRequest - request = ${request.url}');
        String requestUrl = request.url;
        String? startsWithStr = appBackendConfig['replaceWechatPayScheme']['startsWith'];
        if (startsWithStr != null && requestUrl.startsWith(startsWithStr)) {
          // 需求：微信支付完成后要返回App
          // 只能自己匹配 redirect_url，修改成domain://格式，并且在xcode中添加scheme
          String domain = _extractRedirectDomain(requestUrl);
          String redirectUrl = _extractRedirectUrl(requestUrl);
          debugPrint('匹配到的 domain = $domain');
          debugPrint('匹配到的 redirectUrl = $redirectUrl');
          if (domain.isNotEmpty && redirectUrl.isNotEmpty) {
            if (redirectUrl != ('redirect_url=$domain://')) {
              debugPrint('替换前 = $requestUrl');
              requestUrl = requestUrl.replaceAll(redirectUrl, 'redirect_url=$domain://');
              debugPrint('替换后 = $requestUrl');
              _controller.loadRequest(Uri.parse(requestUrl), headers: {"Referer": domain});
              return NavigationDecision.prevent;
            }
          }
        }
        if(CQPaySchemeHandler.canHandle(request.url)) {
          CQPaySchemeHandler.handleUrl(request.url);
          return NavigationDecision.prevent;
        }
        return NavigationDecision.navigate;
      },
    ));
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.initCallBack?.call();
    });
  }

  String _extractRedirectUrl(String paymentUrl) {
    // 正则匹配 redirect_url= 及其后的值（直到下一个 & 或字符串结束）
    String? regExpStr = appBackendConfig['replaceWechatPayScheme']['extractRedirectUrl'];
    if (regExpStr == null) return '';
    RegExp regex = RegExp(regExpStr);

    final match = regex.firstMatch(paymentUrl);
    return match?.group(0) ?? ''; // 返回完整匹配部分
  }

  String _extractRedirectDomain(String url) {
    // 匹配 redirect_url= 后面的域名（直到遇到 /、& 或字符串结束）
    String? regExpStr = appBackendConfig['replaceWechatPayScheme']['extractRedirectDomain'];
    if (regExpStr == null) return '';
    RegExp regex = RegExp(regExpStr);
    final match = regex.firstMatch(url);
    return match?.group(1) ?? '';
  }

  void _weChatPay(String url, String domain) {
    if (!domain.startsWith('http')) {
      domain = 'https://$domain';
    }
    Toast.showLoading();
    try {
      Uri uri = Uri.parse(url);
      _controller.loadRequest(uri, headers: {"Referer": domain});
    } catch (_) {
      Toast.closeLoading();
    }
    Future.delayed(Duration(seconds: 10), () {
      Toast.closeLoading();
    });
  }

  void _aliPay(String formData) {
    Toast.showLoading();
    try {
      _controller.loadHtmlString(formData);
    } catch (_) {
      Toast.showLoading();
    }
    Future.delayed(Duration(seconds: 10), () {
      Toast.closeLoading();
    });
  }

  void _didChangeAppLifecycleState(AppLifecycleState lifecycleState) async {
    if (lifecycleState == AppLifecycleState.resumed) {
      Toast.closeLoading();
      _loadBlank();
      widget.controller._appResumed?.call();
      CQPayWebViewUtil._removeOverlayEntry();
    }
  }

  _loadBlank() {
    // 拉起微信或者支付宝后，返回app时重置当前webview的内容
    _controller.loadRequest(Uri.parse('about:blank'));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppLifecycleWidget(
          didChangeAppLifecycleState: _didChangeAppLifecycleState,
          child: Container(
            width: double.maxFinite,
            height: 1,
            color: Colors.transparent,
            // child: WebViewWidget(controller: _controller),
          ),
        ),
      ],
    );
  }
}
