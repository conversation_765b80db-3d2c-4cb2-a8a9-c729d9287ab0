import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CupertinoMenuItemWidget extends StatelessWidget {
  final String title;
  final Widget? trailing;
  final bool showTrailingArrow;
  final VoidCallback onTap;
  const CupertinoMenuItemWidget({super.key, required this.title, this.trailing, required this.onTap, this.showTrailingArrow = true});

  @override
  Widget build(BuildContext context) {
    return CupertinoButton(
      padding: EdgeInsets.zero,
      onPressed: onTap,
      child: CupertinoListTile(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 26.sp,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
        trailing: Row(
          children: [
            trailing ?? const SizedBox.shrink(),
            if (showTrailingArrow)
              Padding(
                padding: EdgeInsets.only(left: 12.w),
                child: Icon(
                  CupertinoIcons.chevron_right,
                  size: 26.sp,
                  color: CupertinoColors.systemGrey,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
