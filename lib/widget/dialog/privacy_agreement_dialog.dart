import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class PrivacyAgreementDialog extends ConsumerStatefulWidget {
  /// 确认按钮点击回调
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  const PrivacyAgreementDialog({super.key, this.onConfirm, this.onCancel});

  @override
  ConsumerState<PrivacyAgreementDialog> createState() => _PrivacyAgreementDialogState();
}

class _PrivacyAgreementDialogState extends ConsumerState<PrivacyAgreementDialog> {
  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 666.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.w),
            ),
            child: Column(
              children: [
                Text("隐私协议"),
                Text("是否同意隐私协议"),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    FilledButton(
                      onPressed: _onCancel,
                      child: const Text("不同意"),
                    ),
                    FilledButton(
                      onPressed: _onConfirm,
                      child: const Text("同意"),
                    ),
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 不同意按钮点击回调
  void _onCancel() {
    if (widget.onCancel == null) {
      if (Platform.isAndroid) {
        SystemNavigator.pop();
      } else {
        exit(0);
      }
    } else {
      widget.onCancel!();
    }
  }

  /// 确认按钮点击回调
  void _onConfirm() {
    context.pop();
    widget.onConfirm?.call();
  }
}
