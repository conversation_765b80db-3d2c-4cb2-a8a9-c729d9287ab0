import 'package:changqing_health_app/util/connect_util.dart';
import 'package:changqing_health_app/widget/my_loading_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// author: Liu<PERSON><PERSON>
// date: 2024/8/27
// desc: 请求错误页面 网络错误、数据错误

class MyErrorWidget extends StatefulWidget {
  final Future<void> Function()? onReload;

  const MyErrorWidget({this.onReload, super.key});

  @override
  State createState() => _MyErrorWidgetState();
}

class _MyErrorWidgetState extends State<MyErrorWidget> {
  bool _isLoading = false;

  void _reload() async {
    if (!await ConnectUtil().hasNetwork) {
      return;
    }
    setState(() {
      _isLoading = true;
    });

    try {
      if (widget.onReload != null) {
        await widget.onReload!();
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return SizedBox(
        width: constraints.maxWidth,
        height: constraints.maxHeight,
        child: _isLoading
            ? const MyLoadingWidget()
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    'An error occurred.',
                    style: TextStyle(color: Colors.grey),
                  ),
                  CupertinoButton(
                    onPressed: _reload,
                    child: const Text(
                      'Reload',
                      style: TextStyle(color: CupertinoColors.link),
                    ),
                  ),
                ],
              ),
      );
    });
  }
}
