import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CqAnimationTabBar extends StatelessWidget {
  final TabController controller;
  final int index;
  final String label;
  final double fontSize;
  final double scaleDelta;
  final Color selectedColor;
  final Color unselectedColor;
  final double? height;
  final EdgeInsetsGeometry? padding;

  const CqAnimationTabBar({
    super.key,
    required this.controller,
    required this.index,
    required this.label,
    this.fontSize = 14,
    this.scaleDelta = 0.15,
    this.selectedColor = Colors.blue,
    this.unselectedColor = Colors.grey,
    this.height,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller.animation!,
      builder: (context, _) {
        final offset = (controller.animation!.value - index).abs().clamp(0.0, 1.0);
        final scale = 1 + (1 - offset) * scaleDelta;
        final color = Color.lerp(unselectedColor, selectedColor, 1 - offset)!;
        final FontWeight fontWeight = _interpolateFontWeight(1 - offset);
        return Container(
          height: height ?? 60.w,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 12.w),
          color: Colors.transparent,
          alignment: Alignment.center,
          child: Transform.scale(
            scale: scale,
            child: Text(
              label,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: fontWeight,
                color: color,
                height: 1.0, // 避免行高带来的影响
              ),
            ),
          ),
        );
      },
    );
  }

  FontWeight _interpolateFontWeight(double t) {
    // 你可以根据需要更换权重值区间
    const startWeight = 400;
    const endWeight = 700;
    final weightValue = (startWeight + (endWeight - startWeight) * t).round();

    // 将数值映射到 FontWeight 枚举
    switch (weightValue) {
      case < 500:
        return FontWeight.w400;
      case < 600:
        return FontWeight.w500;
      case < 700:
        return FontWeight.w600;
      default:
        return FontWeight.w700;
    }
  }
}
