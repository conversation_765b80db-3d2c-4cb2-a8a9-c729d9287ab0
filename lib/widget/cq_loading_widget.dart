

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

class CQLoadingWidget extends StatelessWidget {
  final double? width;
  const CQLoadingWidget({super.key, this.width});

  @override
  Widget build(BuildContext context) {
    return Lottie.asset('assets/images/loading/cq_loading_icon.json', width: width ?? 160.w);
  }
}
