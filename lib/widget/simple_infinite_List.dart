import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

// author: Liu<PERSON><PERSON>
// date: 2024/9/29
// desc: 基于 infinite_scroll_pagination 插件封装的分页 ListView
///
/// 使用说明：
/// 1. 创建一个  PagingController<int, String>: int 表示页码，String 表示item的数据模型
///   // firstPageKey 表示初始页码
///   final _pagingController = PagingController<int, String>(firstPageKey: 1);
///
/// 2. 构建一个 SimpleInfiniteList<String>
///   // 可以使用 _pagingController.itemList 来获取当期列表中的所有数据
///   Widget _buildSimpleInfiniteList() {
///     return SimpleInfiniteList<String>(
///       pagingController: _pagingController,
///       // 加载数据的 Future 方法
///       loadData: (int pageKey) => ref.read(getUserListProvider(pageKey: pageKey).future),
///       itemBuilder: (context, item, index) {
///         return ListTile(title: Text("$item $index"));
///       },
///     );
///   }
///
///
///
///
class SimpleInfiniteList<T> extends ConsumerStatefulWidget {
  final PagingController<int, T> pagingController;
  final Future<List<T>> Function(int pageKey) loadData;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context)? firstPageProgressIndicatorBuilder;
  final Widget Function(BuildContext context)? newPageProgressIndicatorBuilder;
  final Widget Function(BuildContext context)? newPageErrorIndicatorBuilder;
  final Widget Function(BuildContext context)? firstPageErrorIndicatorBuilder;
  final Widget Function(BuildContext context)? noItemsFoundIndicatorBuilder;
  final Widget Function(BuildContext context)? noMoreItemsIndicatorBuilder;

  const SimpleInfiniteList({
    required this.pagingController,
    required this.loadData,
    required this.itemBuilder,
    this.firstPageProgressIndicatorBuilder,
    this.newPageProgressIndicatorBuilder,
    this.newPageErrorIndicatorBuilder,
    this.firstPageErrorIndicatorBuilder,
    this.noItemsFoundIndicatorBuilder,
    this.noMoreItemsIndicatorBuilder,
    super.key,
  });

  @override
  ConsumerState createState() => _SimpleInfiniteListState<T>();
}

class _SimpleInfiniteListState<T> extends ConsumerState<SimpleInfiniteList<T>> {
  static const _itemSize = 10;

  PagingController<int, T> get _pagingController => widget.pagingController;

  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _pagingController.addPageRequestListener((pageKey) {
      _fetchData(pageKey);
    });
  }

  /// 获取分页数据
  /// [pageKey] 页码
  Future<void> _fetchData(int pageKey) async {
    // 如果正在刷新，跳过加载更多的请求
    if (_isRefreshing && pageKey != _pagingController.firstPageKey) {
      return;
    }

    try {
      final List<T> newItems = await widget.loadData(pageKey);

      // 如果正在刷新，只处理第一页的数据
      if (_isRefreshing && pageKey != _pagingController.firstPageKey) {
        return;
      }

      final bool isLastPage = newItems.length < _itemSize;
      if (isLastPage) {
        _pagingController.appendLastPage(newItems);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(newItems, nextPageKey);
      }
    } catch (error) {
      // 只有在不是刷新状态时才设置错误
      if (!_isRefreshing) {
        _pagingController.error = error;
      }
    }
  }

  /// 刷新数据
  Future<void> _refresh() async {
    _isRefreshing = true;

    try {
      // 重置控制器到初始状态
      _pagingController.refresh();

      // // 等待一小段时间确保刷新状态生效
      // await Future.delayed(const Duration(milliseconds: 100));

      // // 重新加载第一页数据
      // final List<T> newItems = await widget.loadData(_pagingController.firstPageKey);
      // final bool isLastPage = newItems.length < _itemSize;

      // if (isLastPage) {
      //   _pagingController.appendLastPage(newItems);
      // } else {
      //   final nextPageKey = _pagingController.firstPageKey + 1;
      //   _pagingController.appendPage(newItems, nextPageKey);
      // }
    } catch (error) {
      _pagingController.error = error;
    } finally {
      _isRefreshing = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator.adaptive(
      color: Color(0xFF34B001),
      onRefresh: _refresh,
      child: PagedListView<int, T>(
        pagingController: _pagingController,
        builderDelegate: PagedChildBuilderDelegate<T>(
          itemBuilder: (BuildContext context, T item, int index) {
            return widget.itemBuilder(context, item, index);
          },
          firstPageProgressIndicatorBuilder: (context) => widget.firstPageProgressIndicatorBuilder != null
              ? widget.firstPageProgressIndicatorBuilder!(context)
              : const Center(child: CupertinoActivityIndicator()),
          newPageProgressIndicatorBuilder: (context) =>
              widget.newPageProgressIndicatorBuilder != null ? widget.newPageProgressIndicatorBuilder!(context) : _newPageProgressIndicator(context),
          newPageErrorIndicatorBuilder: (context) =>
              widget.newPageErrorIndicatorBuilder != null ? widget.newPageErrorIndicatorBuilder!(context) : _newPageErrorIndicator(context),
          firstPageErrorIndicatorBuilder: (context) =>
              widget.firstPageErrorIndicatorBuilder != null ? widget.firstPageErrorIndicatorBuilder!(context) : _newPageErrorIndicator(context),
          noItemsFoundIndicatorBuilder: (context) =>
              widget.noItemsFoundIndicatorBuilder != null ? widget.noItemsFoundIndicatorBuilder!(context) : _noItemsFoundIndicator(context),
          noMoreItemsIndicatorBuilder: (context) =>
              widget.noMoreItemsIndicatorBuilder != null ? widget.noMoreItemsIndicatorBuilder!(context) : _noMoreItemsIndicator(context),
        ),
      ),
    );
  }

  /// 加载中
  Widget _newPageProgressIndicator(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 24.w,
            height: 24.w,
            child: CircularProgressIndicator(color: Colors.grey, strokeWidth: 3.w),
          ),
          SizedBox(width: 16.w),
          Text('加载中...', style: TextStyle(fontSize: 24.sp, color: Colors.grey)),
        ],
      ),
    );
  }

  /// 没有更多数据
  Widget _noMoreItemsIndicator(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('没有更多数据', style: TextStyle(fontSize: 24.sp, color: Color(0xFF999999))),
        ],
      ),
    );
  }

  /// 加载失败
  Widget _newPageErrorIndicator(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [Text('加载失败', style: TextStyle(fontSize: 24.sp, color: Colors.grey))],
      ),
    );
  }

  /// 没有数据
  Widget _noItemsFoundIndicator(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [Text('暂无数据', style: TextStyle(fontSize: 24.sp, color: Colors.grey))],
      ),
    );
  }

  @override
  void dispose() {
    _pagingController.dispose();
    super.dispose();
  }
}
