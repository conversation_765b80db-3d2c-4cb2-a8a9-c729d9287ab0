import 'package:changqing_health_app/widget/cq_loading_widget.dart';
import 'package:flutter/material.dart' hide RefreshIndicator, RefreshIndicatorState;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

typedef CQRefreshHeaderWidget = Widget? Function(BuildContext context);

/// 分页数据加载的基础刷新组件（含下拉刷新、上拉加载）
/// 泛型 T 为列表项类型
class CQSmartRefresherWidget<T> extends StatefulWidget {
  /// 每页数量（默认 20）
  final int pageSize;

  /// 请求分页数据（提供页码和每页数量），返回数据列表
  final Future<List<T>> Function(int pageNo, int pageSize) onFetch;

  /// 构建每个 item 的 widget
  final Widget Function(BuildContext context, T item, int index)? itemBuilder;

  /// 空状态占位（可选）
  final Widget? emptyBuilder;

  /// 错误状态占位（可选）
  final Widget? errorBuilder;

  /// 顶部widget
  final CQRefreshHeaderWidget? headerWidget;

  final Widget? bodyWidget;

  /// 是否启用上拉加载更多
  /// 默认为true，但是也会判断 请求的数据是否为空，如果为空则展示 无更多数据 提示
  final bool enablePullUp;

  const CQSmartRefresherWidget({
    super.key,
    required this.onFetch,
    this.itemBuilder,
    this.pageSize = 20,
    this.emptyBuilder,
    this.errorBuilder,
    this.headerWidget,
    this.bodyWidget,
    this.enablePullUp = true,
  });

  @override
  State<CQSmartRefresherWidget<T>> createState() => _CQSmartRefresherWidgetState<T>();
}

class _CQSmartRefresherWidgetState<T> extends State<CQSmartRefresherWidget<T>> {
  final RefreshController _controller = RefreshController(initialRefresh: false);

  int _pageNo = 1;
  List<T> _items = [];
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitial();
    });
  }

  Future<void> _loadInitial() async {
    setState(() {
      _pageNo = 1;
      _isLoading = true;
      _hasError = false;
    });

    try {
      final data = await _onRefresh();
      if (data == null) {
        setState(() => _hasError = true);
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<List<T>?> _onRefresh() async {
    try {
      final data = await widget.onFetch(1, widget.pageSize);
      setState(() {
        _items = data;
        _pageNo = 1;
      });
      _controller.refreshCompleted();
      if (_items.isNotEmpty) {
        _controller.resetNoData();
      } else {
        // 底部提示无更多数据
        _controller.loadNoData();
      }
      return data;
    } catch (_) {
      _controller.refreshCompleted();
      return null;
    }
  }

  Future<void> _onLoadMore() async {
    try {
      final nextPage = _pageNo + 1;
      final data = await widget.onFetch(nextPage, widget.pageSize);
      if (data.isEmpty) {
        _controller.loadNoData();
        // _controller.loadComplete();
      } else {
        setState(() {
          _items.addAll(data);
          _pageNo = nextPage;
        });
        _controller.loadComplete();
        if (data.isEmpty) {
          // 底部提示无更多数据
          _controller.loadNoData();
        }
      }
    } catch (_) {
      _controller.loadFailed();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: Container(
          alignment: Alignment.center,
          color: Colors.transparent,
          child: CQLoadingWidget(),
        ),
      );
    }

    return SmartRefresher(
      controller: _controller,
      enablePullDown: true,
      enablePullUp: widget.enablePullUp && _items.isNotEmpty,
      header: ClassicHeader(
        refreshStyle: RefreshStyle.Follow,
        // iOS 风格
        refreshingIcon: Container(
          color: Colors.transparent,
          child: CQLoadingWidget(),
        ),
        // iOS 风格加载指示器
        completeIcon: null,
        idleIcon: null,
        releaseIcon: null,
        idleText: '下拉刷新',
        refreshingText: '',
        completeText: '刷新完成',
        failedText: '刷新失败',
        releaseText: '松开刷新',
        spacing: 0,
      ),
      footer: ClassicFooter(
        loadStyle: _controller.footerStatus == LoadStatus.noMore ? LoadStyle.HideAlways : LoadStyle.ShowWhenLoading,
        // loadStyle: LoadStyle.ShowWhensLoading,
        noDataText: '暂无更多数据',
        idleText: '',
        loadingText: '',
        failedText: '',
        canLoadingText: '松开后加载更多数据',
        canLoadingIcon: null,
        loadingIcon: CQLoadingWidget(),
        spacing: 0,
        idleIcon: null,
      ),
      onRefresh: _onRefresh,
      onLoading: _onLoadMore,
      child: _bodyWidget(),
    );
  }


  Widget _bodyWidget() {

    if (_hasError) {
      return widget.errorBuilder ??
          Center(
            child: TextButton(
              onPressed: _loadInitial,
              child: const Text('加载失败，点击重试'),
            ),
          );
    }

    if (_items.isEmpty) {
      if (widget.emptyBuilder != null) {
        return widget.emptyBuilder!;
      }
      if (widget.headerWidget != null) {
        return Column(
          children: [
            widget.headerWidget!(context) ?? SizedBox.shrink(),
            Expanded(child: _noDataWidget()),
          ],
        );
      }
      return widget.emptyBuilder ?? _noDataWidget();
    }

    if (widget.bodyWidget != null) {
      return widget.bodyWidget!;
    }
    if (widget.itemBuilder == null) {
      return Container();
    }
    if (widget.headerWidget != null) {
      return CustomScrollView(
        slivers: [
          SliverToBoxAdapter(child: widget.headerWidget!(context) ?? SizedBox.shrink()),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (BuildContext context, int index) => widget.itemBuilder!(context, _items[index], index),
              childCount: _items.length,
            ),
          ),
        ],
      );
    }
    return ListView.builder(
      itemCount: _items.length,
      itemBuilder: (context, index) => widget.itemBuilder!(context, _items[index], index),
    );
  }


  Widget _noDataWidget() {
    return Container(
      alignment: Alignment.center,
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/course/course_empty_icon.png',
            width: 460.w,
            height: 254.w,
          ),
          SizedBox(height: 20.w),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 32.sp,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }
}
