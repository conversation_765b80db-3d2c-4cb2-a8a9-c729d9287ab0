import 'package:flutter/material.dart';

/// 使用了 StrutStyle ，可以使其上下居中
class CqAlignText extends StatelessWidget {
  final String data;
  final Color? color;
  final double? fontSize;
  final FontWeight? fontWeight;

  const CqAlignText(
    this.data, {
    super.key,
    this.color,
    this.fontSize,
    this.fontWeight,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      data,
      style: TextStyle(color: color, fontSize: fontSize, fontWeight: fontWeight),
      strutStyle: StrutStyle(fontWeight: fontWeight, fontSize: fontSize, forceStrutHeight: true),
    );
  }
}
