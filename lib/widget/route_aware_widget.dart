import 'package:chang<PERSON>_health_app/app/application.dart';
import 'package:flutter/material.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2/16/21
/// desc:
//

class RouteAwareWidget extends StatefulWidget {
  final Widget child;
  final Function? didPopNext;
  final Function? didPush;
  final Function? didPop;
  final Function? didPushNext;

  RouteAwareWidget({
    required this.child,
     this.didPopNext,
     this.didPush,
     this.didPop,
     this.didPushNext,
  });

  @override
  _RouteAwareWidgetState createState() => _RouteAwareWidgetState();
}

class _RouteAwareWidgetState extends State<RouteAwareWidget> with RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    var route = ModalRoute.of(context);
    if (route is PageRoute<dynamic>) {
      routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    super.dispose();
    routeObserver.unsubscribe(this);
  }

  @override
  Widget build(BuildContext context) => widget.child;

  @override
  void didPop() {
    if (widget.didPop != null) widget.didPop!();
  }

  @override
  void didPopNext() {
    if (widget.didPopNext != null) widget.didPopNext!();
  }

  @override
  void didPush() {
    if (widget.didPush != null) widget.didPush!();
  }

  @override
  void didPushNext() {
    if (widget.didPushNext != null) widget.didPushNext!();
  }
}
