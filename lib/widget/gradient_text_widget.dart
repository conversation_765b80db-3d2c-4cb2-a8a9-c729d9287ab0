import 'package:flutter/material.dart';

/// 渐变文字组件
class GradientText extends StatelessWidget {
  final String text;
  final List<Color> colors;
  final AlignmentGeometry? begin;
  final AlignmentGeometry? end;
  final GradientType gradientType;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const GradientText({
    super.key,
    required this.text,
    required this.colors,
    this.begin,
    this.end,
    this.gradientType = GradientType.linear,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (Rect bounds) {
        switch (gradientType) {
          case GradientType.linear:
            return LinearGradient(
              colors: colors,
              begin: begin ?? Alignment.topLeft,
              end: end ?? Alignment.bottomRight,
            ).createShader(bounds);
          case GradientType.radial:
            return RadialGradient(
              colors: colors,
              center: begin as Alignment? ?? Alignment.center,
              radius: 1.0,
            ).createShader(bounds);
        }
      },
      child: Text(
        text,
        style: style?.copyWith(color: Colors.white) ?? TextStyle(color: Colors.white),
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      ),
    );
  }
}

/// 渐变类型
enum GradientType {
  linear, // 线性渐变
  radial, // 径向渐变
}
