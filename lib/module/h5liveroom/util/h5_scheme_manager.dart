import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/home/<USER>/home_route.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart';
import 'package:changqing_health_app/module/navigator/provider/bottom_bar_provider.dart';
import 'package:changqing_health_app/module/navigator/route/navigator_route.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/webview/page/common_web_page.dart';
import 'package:changqing_health_app/webview/route/common_web_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// cqapp://toLivePage?liveUrl={encode(sourceUrl)}

class H5SchemeManager {
  static const String scheme = 'cqapp';

  static const String toLivePage = 'toLivePage';

  static ProviderSubscription<bool>? _loginSubscription;

  static void handleHScheme(Uri uri, WidgetRef ref) async {
    if (uri.scheme != scheme) {
      return;
    }
    if (uri.host.toLowerCase() == toLivePage.toLowerCase()) {
      String? liveUrl = uri.queryParameters['liveUrl'];
      if (liveUrl != null) {
        // 校验登录状态
        final isLogin = ref.read(loginProvider);
        if (isLogin) {
          _pushH5RoomPage(liveUrl, ref);
        } else {
          _loginSubscription?.close();
          _loginSubscription = null;
          _loginSubscription = ref.listenManual(loginProvider, (previous, next) async {
            if (next == true) {
              await Future.delayed(Duration(seconds: 1));
              _pushH5RoomPage(liveUrl, ref);
              _loginSubscription?.close();
              _loginSubscription = null;
            }
          });
          // 判断当前是不是在登录页面的路由
          final currentRoute = GoRouter.of(globalContext!).state.path;
          if (currentRoute != ref.read(getLoginRouteProvider)) {
            globalContext!.push(ref.read(getLoginRouteProvider));
          }
        }
      }
    }
  }

  static String _replaceToken(String liveUrl){
    final token = SpUtil().get(spAppToken);
    final appKey = cqjkAppKey;
    liveUrl = liveUrl.replaceAll('APP_TOKEN', token);
    liveUrl = liveUrl.replaceAll('APP_KEY', appKey);
    String sourceUrl = Uri.decodeComponent(liveUrl);
    debugPrint('sourceUrl = $sourceUrl');
    return sourceUrl;
  }


  static _pushH5RoomPage(String liveUrl, WidgetRef ref) {
    String sourceUrl = _replaceToken(liveUrl);
    HomeRoute().go(globalContext!);
    try {
      ref.read(bottomBarProvider.notifier).toggle(homeBarModel);
    }catch(_){}
    CommonWebRoute(url: sourceUrl, title: '直播').push(globalContext!);
  }


  /// 在新的页面打开直播url
  static openLiveUrlOnNewPage(String liveUrl, {String? title}){
    // 防止短时间内多次跳转webview
    if(DebounceHelper.shouldExecute()) {
      String sourceUrl = _replaceToken(liveUrl);
      CommonWebRoute(url: sourceUrl, title: title ?? '直播').push(globalContext!);
    }

  }
}


// 防抖
class DebounceHelper {
  static DateTime? _lastActionTime;

  static bool shouldExecute([Duration duration = const Duration(milliseconds: 800)]) {
    final now = DateTime.now();
    if (_lastActionTime == null || now.difference(_lastActionTime!) > duration) {
      _lastActionTime = now;
      return true;
    }
    return false;
  }
}