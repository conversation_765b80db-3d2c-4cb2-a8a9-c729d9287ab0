import 'package:changqing_health_app/app/init.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/agreement/dialog/agreement_dialog.dart';
import 'package:changqing_health_app/module/course_table/route/course_table_route.dart';
import 'package:changqing_health_app/module/home/<USER>/home_route.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/navigator/route/navigator_route.dart';
import 'package:changqing_health_app/module/other/check_version/app_upgrade_dialog.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/util/youzan_util.dart';
import 'package:changqing_health_app/widget/cq_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 入口页面
class RootPage extends ConsumerStatefulWidget {
  const RootPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _RootPageState();
}

class _RootPageState extends ConsumerState<RootPage> {
  bool _precacheImageSuccess = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      _precacheImage();
    });
    super.initState();
  }

  void _initState() {
    final isAgreeAgreement = SpUtil().get(spFirstInstallAgreement) ?? false;
    if (!isAgreeAgreement) {
      _showAgreementDialog();
    } else {
      _initAppData();
    }
  }

  Future _precacheImage() async {
    await precacheImage(AssetImage('assets/images/app-launch-image.png'), context, onError: (_, __) {
      _precacheSuccess();
    });
    _precacheSuccess();
  }

  void _precacheSuccess() {
    FlutterNativeSplash.remove();
    _precacheImageSuccess = true;
    setState(() {});
    _initState();
  }

  Future<void> _initAppData() async {

    try {
      await AppBuryingPointUtil.init(domain: EnvConfig().buryingPointDomain!, serviceName: 'cqjkapp');
      await ref.read(deviceInfoProvider.future);
      await ref.read(appVersionProviderProvider.future);
      await ref.read(appVersionProviderProvider.notifier).checkVersion();
      if (ref.read(appVersionProviderProvider.notifier).versionInfo == null) {
        await Future.delayed(Duration(seconds: 1));
        await ref.read(appVersionProviderProvider.notifier).checkVersion();
      }
      if (await _needUpdate()) {
        return;
      }
      await _refreshUserInfo();
      if (ref.read(loginProvider)) {
        YouzanUtil.login(userId: SpUtil().get(spAppMid));
      }
    } catch (_) {}
    if (mounted) {
      if (ref.read(loginProvider)) {
        CourseTableRoute().go(context);
      } else {
        HomeRoute().go(context);
      }
    }
  }

  Future<bool> _needUpdate() async {
    if (ref.read(appVersionProviderProvider.notifier).isForceUpdate) {
      AppUpgradeDialog.show(context, ref);
      return true;
    }
    return false;
  }

  Future<void> _refreshUserInfo() async {
    try {
      if (ref.read(loginProvider)) {
        ref.refresh(userProvider);
      }
      ref.read(appBackendConfigProvider.future);
    } catch (_) {}
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(appVersionProviderProvider);
    if (!_precacheImageSuccess) {
      return SizedBox.shrink();
    }
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Image.asset('assets/images/app-launch-image.png', width: 1.sw, height: 1.sh, fit: BoxFit.cover),
          Positioned.fill(child: Center(child: CQLoadingWidget())),
        ],
      ),
    );
  }

  void _showAgreementDialog() {
    AgreementDialog.showDialog(context, onAgree: (isAgree) async {
      if (isAgree) {
        SpUtil().set(spFirstInstallAgreement, true);
        initThirdPartySdk();
        _initAppData();
      }
    });
  }
}
