import 'package:changqing_health_app/module/root/page/root_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

part 'root_route.g.dart';

@TypedGoRoute<RootRoute>(path: '/')
class RootRoute extends GoRouteData {
  const RootRoute();
  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const NoTransitionPage(child: RootPage());
  }
}
