import 'package:cached_network_image/cached_network_image.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/liveroom/provider/live_room_provider.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/study/model/study_model.dart';
import 'package:changqing_health_app/module/video/route/video_route.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';


class LiveReplayWidget extends ConsumerWidget {
  final StudyReplayModel data;
  const LiveReplayWidget({super.key, required this.data});
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String time = data.videoCreateTime;
    debugPrint('time = $time');
    try {
      String dateTimeStr = time;
      DateTime dt = DateTime.parse(dateTimeStr);
      String dateOnly = DateFormat('yyyy-MM-dd').format(dt); // "2025-02-28"
      time = dateOnly;
    }catch(_){}
    return Container(
      width: 686.w,
      height: 280.w,
      margin: EdgeInsets.only(left: 32.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 32.w, top: 32.w, right: 32.w),
            child: Row(
              children: [
                Text(
                  '【${data.chapterName}】回放',
                  style: TextStyle(
                    fontSize: 30.sp,
                    color: const Color(0xff5c77df),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Expanded(child: Container()),
                Text(time, style: TextStyle(fontSize: 24.sp, color: const Color(0xff666666))),
              ],
            ),
          ),
          24.gap,
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SizedBox(width: 32.w),
                Container(
                  width: 240.w,
                  height: 170.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20.w),
                    border: Border.all(color: const Color(0xffe5e5e5)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20.w),
                    child: CQCachedNetworkImage(
                      imageUrl: data.chapterPic,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Center(child: CupertinoActivityIndicator()),
                      errorWidget: (context, url, error) => const Icon(Icons.error),
                    ),
                  ),
                ),
                Expanded(child: Container()),
                _contentWidget(context, ref),
                32.gap,
              ],
            ),
          ),
          24.gap,
        ],
      ),
    );
  }

  Widget _contentWidget(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () async {
        if(ref.read(appVersionProviderProvider.notifier).isReview) {
          VideoRoute(
          videoUrl: data.appId,
          categoryName: data.chapterName,
          title: data.chapterName,
          subTitle: '',
        ).push(context);
          return;
        }
        ref.read(liveRoomProvider).pushLiveRoomReplay(data, context);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 44.w, vertical: 16.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(50.w),
          gradient: const LinearGradient(
            colors: [
              Color(0xFFe6e9f9),
              Color(0xFF5d7ada),
            ],
          ),
        ),
        child: Text(
          '观看回放',
          style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold, color: Colors.white),
        ),
      ),
    );
  }
}
