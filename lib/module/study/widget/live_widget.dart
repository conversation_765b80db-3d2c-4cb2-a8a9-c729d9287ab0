import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/liveroom/provider/live_room_provider.dart';
import 'package:changqing_health_app/module/study/model/study_model.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LiveWidget extends ConsumerStatefulWidget {
  final StudyModel studyData;
  const LiveWidget({super.key, required this.studyData});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<LiveWidget> {
  StudyModel get _studyData => widget.studyData;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // if (_studyData.liveStatus == LiveStatus.notStarted) {
        //   Toast.show('直播未开始');
        //   return;
        // }
        // if (_studyData.liveStatus == LiveStatus.ended) {
        //   Toast.show('直播已结束');
        //   return;
        // }
        ref.read(liveRoomProvider).pushLiveRoom(_studyData, context);
      },
      child: Container(
        width: 686.w,
        height: 300.w,
        margin: EdgeInsets.only(left: 32.w),
        decoration: BoxDecoration(
          // color: Color(0xFF00aa50).withValues(alpha: 0.3),
          color: Colors.white,
          borderRadius: BorderRadius.circular(30.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 32.w, top: 32.w),
              child: Text('今日直播',
                  style: TextStyle(
                    fontSize: 30.sp,
                    color: const Color(0xff00aa50),
                    fontWeight: FontWeight.bold,
                  )),
            ),
            32.gap,
            Padding(
              padding: EdgeInsets.only(left: 32.w, top: 12.w),
              child: Text(_studyData.periodName, style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal)),
            ),
            32.gap,
            Padding(
              padding: EdgeInsets.only(left: 32.w, right: 32.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _studyData.liveStatus.desc,
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: const Color(0xff666666),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 44.w, vertical: 16.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50.w),
                      color: _studyData.liveStatus == LiveStatus.notStarted ? const Color(0xFFEFEFEF) : null,
                      gradient: _studyData.liveStatus == LiveStatus.started
                          ? const LinearGradient(
                              colors: [
                                Color(0xFF74f03c),
                                Color(0xFF00aa50),
                              ],
                            )
                          : null,
                    ),
                    child: Text(
                      _studyData.startTime.split(' ').last,
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                        color: _studyData.liveStatus == LiveStatus.started ? Colors.white : const Color(0xffc9c9c9),
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
