import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/study/provider/study_provider.dart';
import 'package:changqing_health_app/module/study/widget/live_replay_widget.dart';
import 'package:changqing_health_app/module/study/widget/live_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';



/// 学习页面
class StudyPage extends ConsumerStatefulWidget {
  const StudyPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<StudyPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(top: context.paddingTop),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 32.w, top: 32.w),
              child: Text(
                ref.read(appVersionProviderProvider.notifier).isReview ? '公开课' : '体验营',
                style: TextStyle(fontSize: 44.sp, fontWeight: FontWeight.normal),
              ),
            ),
            32.gap,
            Expanded(
              child: _buildContentWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentWidget() {
    return ref.watch(studyTabProvider).when(
          data: (data) {
            debugPrint('_build -- _buildContentWidget');
            return SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLive(),
                  32.gap,
                  _buildPlayback(),
                  32.gap,
                  // _buildPreview(),
                ],
              ),
            );
          },
          error: (error, stack) => _noDataWidget('暂无直播/回放数据'),
          loading: () => LayoutBuilder(builder: (context, siz) {
            return Container(
              alignment: Alignment.center,
              color: Colors.transparent,
              height: siz.maxHeight,
              child: CupertinoActivityIndicator(),
            );
          }),
        );
  }

  Widget _buildLive() {
    return ref.watch(studyTabProvider).when(
          data: (data) {
            if (data.studyList.isEmpty) {
              if (ref.read(appVersionProviderProvider.notifier).isReview) {
                return SizedBox.shrink();
              }
              return _noDataWidget('暂无直播数据');
            }
            return Column(
              children: data.studyList
                  .map((e) => Padding(
                        padding: EdgeInsets.only(bottom: 32.w),
                        child: LiveWidget(studyData: e),
                      ))
                  .toList(),
            );
          },
          error: (error, stack) => _noDataWidget('暂无直播数据'),
          loading: () => LayoutBuilder(builder: (context, siz) {
            return Container(
              alignment: Alignment.center,
              height: siz.maxHeight,
              child: CupertinoActivityIndicator(),
            );
          }),
        );
  }

  Widget _buildPlayback() {
    debugPrint('_build -- _buildPlayback');
    return ref.watch(studyTabProvider).when(
          data: (data) {
            if (data.studyReplayList.isEmpty) {
              return _noDataWidget('暂无回放数据');
            }
            return Column(
              children: data.studyReplayList
                  .map((e) => Padding(
                        padding: EdgeInsets.only(bottom: 32.w),
                        child: LiveReplayWidget(data: e),
                      ))
                  .toList(),
            );
          },
          error: (error, stack) => _noDataWidget('暂无回放数据'),
          loading: () => SizedBox.shrink(),
        );
  }

  Widget _noDataWidget(String text) {
    return Center(
      child: Container(
        width: 686.w,
        height: 300.w,
        decoration: BoxDecoration(
          // color: Color(0xFF00aa50).withValues(alpha: 0.3),
          color: Colors.white,
          borderRadius: BorderRadius.circular(30.w),
        ),
        alignment: Alignment.center,
        child: Text(text, style: TextStyle(color: Colors.black26, fontSize: 32.sp)),
      ),
    );
  }

  Widget _buildPreview() {
    return Container(
      width: 686.w,
      height: 200.w,
      margin: EdgeInsets.only(left: 32.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(30.w),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 32.w, top: 32.w, right: 32.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '课程预告',
                  style: TextStyle(
                    fontSize: 30.sp,
                    color: const Color(0xff48505d),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text('2025-04-10 20:00', style: TextStyle(fontSize: 24.sp, color: const Color(0xff666666))),
              ],
            ),
          ),
          32.gap,
          Padding(
            padding: EdgeInsets.only(left: 32.w, top: 12.w),
            child: Text('第八课：家庭营养搭配', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal)),
          ),
        ],
      ),
    );
  }
}
