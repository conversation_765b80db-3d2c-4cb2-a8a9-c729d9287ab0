
import 'package:changqing_health_app/module/study/page/study_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 学习路由
class StudyBranchSellData extends StatefulShellBranchData {
  const StudyBranchSellData();
}

class StudyRoute extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const StudyPage();
  }
}

