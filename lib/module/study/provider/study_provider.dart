import 'dart:async';

import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/study/api/study_api.dart';
import 'package:changqing_health_app/module/study/model/study_model.dart';
import 'package:changqing_health_app/module/study/model/study_tab_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'study_provider.g.dart';


@Riverpod(keepAlive: true)
class StudyTab extends _$StudyTab {
  @override
  FutureOr<StudyTabModel> build() async {
    return await refreshStudyTabData();
  }

  Future refreshStudyTabData() async {
    final studyList = await StudyApi.getStudyData();
    final studyReplayList = await getStudyData();
    StudyTabModel studyTabModel = StudyTabModel(studyList: studyList, studyReplayList: studyReplayList);
    state = AsyncValue.data(studyTabModel);
    return studyTabModel;
  }

  Future<List<StudyReplayModel>> getStudyData() async {
     if(ref.read(appVersionProviderProvider.notifier).isReview) {
      return await StudyApi.reviewStudyReplayData();
    }else {
      return await StudyApi.getStudyReplayData();
    }
  }
}
