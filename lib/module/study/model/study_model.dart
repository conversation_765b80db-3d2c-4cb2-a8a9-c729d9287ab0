import 'package:freezed_annotation/freezed_annotation.dart';

part 'study_model.freezed.dart';
part 'study_model.g.dart';

enum LiveStatus {
  notStarted(1, '未开始'),
  started(2, '进行中'),
  ended(3, '已结束'),
  ;

  const LiveStatus(this.status, this.desc);

  final int status;
  final String desc;
}

@freezed
class StudyModel with _$StudyModel {
  const factory StudyModel({
    @JsonKey(name: 'period_id') required int periodId,
    @JsonKey(name: 'period_name') required String periodName,
    @<PERSON>son<PERSON>ey(name: 'room_id') required String roomId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'start_time') required String startTime,
    @J<PERSON><PERSON><PERSON>(name: 'end_time') required String endTime,
    // @Json<PERSON>ey(name: 'live_status') required int liveStatus,
    @<PERSON><PERSON><PERSON>ey(
      name: 'live_status',
      fromJson: _liveStatusFromJson,
      toJson: _liveStatusToJson,
    )
    required LiveStatus liveStatus,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'appId') required String appId,
    @JsonK<PERSON>(name: 'appIdKey') required String appIdKey,
  }) = _StudyModel;

  factory StudyModel.fromJson(Map<String, dynamic> json) => _$StudyModelFromJson(json);
}

LiveStatus _liveStatusFromJson(int status) => LiveStatus.values.firstWhere((e) => e.status == status);

int _liveStatusToJson(LiveStatus status) => status.status;

@freezed
class StudyReplayModel with _$StudyReplayModel {
  const factory StudyReplayModel({
    @JsonKey(name: 'period_id') required int periodId,
    @JsonKey(name: 'period_name') required String periodName,
    @JsonKey(name: 'room_id') required String roomId,
    @JsonKey(name: 'chapter_id') required int chapterId,
    @JsonKey(name: 'chapter_name') required String chapterName,
    @JsonKey(name: 'chapter_pic') required String chapterPic,
    @JsonKey(name: 'video_id') required int videoId,
    @JsonKey(name: 'appId') required String appId,
    @JsonKey(name: 'appIdKey') required String appIdKey,
    @JsonKey(name: 'video_create_time') required String videoCreateTime,
  }) = _StudyReplayModel;

  factory StudyReplayModel.fromJson(Map<String, dynamic> json) => _$StudyReplayModelFromJson(json);
}
