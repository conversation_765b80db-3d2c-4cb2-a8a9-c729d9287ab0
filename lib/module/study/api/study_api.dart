
import 'package:changqing_health_app/module/study/model/study_model.dart';
import 'package:changqing_health_app/service/http/api_client.dart';

class StudyApi {
  StudyApi._();

  static Future<List<StudyModel>> getStudyData() async {
    try {
      final response = await ApiClient.get('/api/cqapp/front/cqjk/live/today');
      final originData = response.data as List<dynamic>;
      final studyData = originData.map((e) => StudyModel.fromJson(e)).toList();
      // final studyData = response.data.map((e) => StudyModel.fromJson(e)).toList();
      return studyData;
    } catch (e) {
      print('Error during getStudyData: $e');
      return [];
    }
  }

  static Future<List<StudyReplayModel>> getStudyReplayData() async {
    try {
      final response = await ApiClient.get('/api/cqapp/front/cqjk/live/replay');
      final originData = response.data as List<dynamic>;
      final studyData = originData.map((e) => StudyReplayModel.fromJson(e)).toList();
      return studyData;
    } catch (e) {
      print('Error during getStudyReplayData: $e');
      return [];
    }
  }
  
  /// 审核模式下获取回放数据
  static Future<List<StudyReplayModel>> reviewStudyReplayData() async {
    try {
      final originData = [
        {
          "period_id": 1,
          "period_name": "家庭用油怎么选择？",
          "room_id": "room_id",
          "chapter_id": 1,
          "chapter_name": "家庭用油怎么选择？",
          "chapter_pic": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-11-28/c5570545-b997-4098-928e-53b8dacb3bee-1.标题：家庭用油怎么选择？.png",
          "video_id": 1,
          "appId": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-11-28/cd78abf2-7056-408b-aec6-6ea09727982f-1.标题：家庭用油怎么选择？.mp4",
          "appIdKey": "appIdKey",
          "video_create_time": "2025-03-21 09:30:00"
        },
        {
          "period_id": 1,
          "period_name": "酱油怎么选",
          "room_id": "room_id",
          "chapter_id": 1,
          "chapter_name": "酱油怎么选",
          "chapter_pic": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/5eae166d-4d61-4d67-b361-ffd58d9b6d90-12.png",
          "video_id": 1,
          "appId": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/19384638-9362-4448-8479-62cd1efc4eac-12.酱油怎么选？_横版.mp4",
          "appIdKey": "appIdKey",
          "video_create_time": "2025-01-15 09:00:00"
        },
        {
          "period_id": 1,
          "period_name": "醋的挑选应该注意哪些呢？",
          "room_id": "room_id",
          "chapter_id": 1,
          "chapter_name": "醋的挑选应该注意哪些呢？",
          "chapter_pic": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/dabf13b5-2017-4f91-8c04-f45b8d4cc68d-13.png",
          "video_id": 1,
          "appId": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/557ab6e9-2479-4fc8-9a9a-80fdd9102f12-13.醋的挑选应该注意哪些呢？.mp4",
          "appIdKey": "appIdKey",
          "video_create_time": "2024-11-04 08:40:00"
        },
        {
          "period_id": 1,
          "period_name": "如何挑选更健康的盐",
          "room_id": "room_id",
          "chapter_id": 1,
          "chapter_name": "如何挑选更健康的盐",
          "chapter_pic": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-08-27/ac5adbb4-8247-42af-b3a2-d608f290fe04-15.png",
          "video_id": 1,
          "appId": "https://cdn-cq-changqing.cqslim.com/community-resource/2024-11-28/cd78abf2-7056-408b-aec6-6ea09727982f-1.标题：家庭用油怎么选择？.mp4",
          "appIdKey": "appIdKey",
          "video_create_time": "2024-07-06 08:30:00"
        },
      ];
      final studyData = originData.map((e) => StudyReplayModel.fromJson(e)).toList();
      return studyData;
    } catch (e) {
      print('Error during getStudyReplayData: $e');
      return [];
    }
  }
}
