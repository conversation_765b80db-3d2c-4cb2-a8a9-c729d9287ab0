import 'dart:io';

import 'package:changqing_health_app/config/enum/env_enum.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/liveroom/route/live_room_route.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/study/model/study_model.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/service/http/request_interceptor.dart';
import 'package:changqing_health_app/util/sp_util.dart';
// import 'package:cq_flutter_live_room/wm_flutter_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final liveRoomProvider = Provider((ref) => LiveRoomProvider(ref: ref));

class LiveRoomProvider {
  final Ref ref;

  LiveRoomProvider({required this.ref});

  void pushLiveRoom(StudyModel studyModel, BuildContext context) {
    // WMLiveConfigureModel configureModel = _getConfigureModel(appId: studyModel.appId, appIdKey: studyModel.appIdKey, roomId: studyModel.roomId);
    // LiveRoomRoute($extra: configureModel).push(context);
  }

  void pushLiveRoomReplay(StudyReplayModel studyReplayModel, BuildContext context) {
    // WMLiveConfigureModel configureModel = _getConfigureModel(
    //   appId: studyReplayModel.appId,
    //   appIdKey: studyReplayModel.appIdKey,
    //   roomId: studyReplayModel.roomId,
    //   videoId: studyReplayModel.videoId,
    // );
    // LiveReplayRoute($extra: configureModel).push(context);
  }

  // WMLiveConfigureModel _getConfigureModel({required String appId, required String appIdKey, required String roomId, int? videoId}) {
  //   WMLiveConfigureModel configureModel = WMLiveConfigureModel(
  //     zhongtaiDomain: EnvConfig().zhongtaiDomain,
  //     dvDomain: EnvConfig().dvDomain,
  //     zhongtaiAppDomain: EnvConfig().zhongtaiAppDomain,
  //     sourceAppId: cqjkAppId,
  //     appId: appId,
  //     appIdKey: appIdKey,
  //     systemVersion: ref.read(deviceInfoProvider).value?.version ?? '',
  //     os: Platform.isIOS ? 'iOS' : 'Android',
  //     mid: SpUtil().get(spAppMid).toString(),
  //     authorization: SpUtil().get(spAppJwt),
  //     roomId: roomId,
  //     appVersion: ref.read(deviceInfoProvider).value?.version ?? '',
  //     env: EnvConfig().env == Env.dev ? WMLiveSDKEnv.dev : WMLiveSDKEnv.prod,
  //     isReview: ref.read(appVersionProviderProvider.notifier).isReview,
  //     userName: ref.read(userProvider).name ?? '',
  //     userImage: ref.read(userProvider).avatar ?? '',
  //     dvToken: SpUtil().get(spAppJwt),
  //     videoId: videoId == null ? null : videoId.toString(),
  //     proxyContent: SpUtil().get(spAppNetworkProxy),
  //     sourceAppIdKey: cqjkAppKey,
  //     cqjkAppHeader: cqjkRequestInterceptorHeader,
  //   );
  //   return configureModel;
  // }
}
