// import 'package:cq_flutter_live_room/live_room/live/wm_live_page.dart';
// import 'package:cq_flutter_live_room/live_room/play_back/wm_live_play_back_page.dart';
// import 'package:cq_flutter_live_room/wm_flutter_plugin.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:go_router/go_router.dart';
//
// part 'live_room_route.g.dart';
//
// @TypedGoRoute<LiveRoomRoute>(path: '/live_room')
// class LiveRoomRoute extends GoRouteData {
//   final WMLiveConfigureModel $extra;
//   const LiveRoomRoute({required this.$extra});
//
//   @override
//   Page<void> buildPage(BuildContext context, GoRouterState state) {
//     return CupertinoPage<void>(
//       child: WMLiveRoomPage(
//         getParameterFuture: () async {
//           return $extra;
//         },
//       ),
//     );
//   }
// }
//
// @TypedGoRoute<LiveReplayRoute>(path: '/live_replay')
// class LiveReplayRoute extends GoRouteData {
//   final WMLiveConfigureModel $extra;
//   const LiveReplayRoute({required this.$extra});
//
//   @override
//   Page<void> buildPage(BuildContext context, GoRouterState state) {
//     return CupertinoPage<void>(
//       child: WMLivePlayBackRoomPage(getParameterFuture: () async {
//         return $extra;
//       }),
//     );
//   }
// }
//
