
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
part 'bottom_bar_model.freezed.dart';

@freezed
class BottomBarModel with _$BottomBarModel {
  const factory BottomBarModel({
     required int id,
     required String label,
     required bool isActive,
     required String icon,
     required String activeIcon,
     required bool needLogin,
     IconData? iconData,
  }) = _BottomBarModel;
}