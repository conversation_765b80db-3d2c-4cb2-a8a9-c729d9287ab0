import 'package:changqing_health_app/module/course_table/route/course_table_route.dart';
import 'package:changqing_health_app/module/home/<USER>/home_route.dart';
import 'package:changqing_health_app/module/navigator/widget/scaffold_with_nav_bar.dart';
import 'package:changqing_health_app/module/user/route/user_route.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
part 'navigator_route.g.dart';

// @TypedStatefulShellRoute<ScaffoldWithNavBarShellRoute>(branches: [
//   // 首页
//   TypedStatefulShellBranch<HomeBranchSellData>(routes: [TypedGoRoute<HomeRoute>(path: '/home')]),
//   // 学习
//   TypedStatefulShellBranch<StudyBranchSellData>(routes: [TypedGoRoute<StudyRoute>(path: '/study')]),
//   // 社区
//   TypedStatefulShellBranch<CommunityBranchSellData>(routes: [TypedGoRoute<CommunityRoute>(path: '/community')]),
//   // 我的
//   TypedStatefulShellBranch<UserBranchSellData>(routes: [TypedGoRoute<UserRoute>(path: '/user')]),
// ])

@TypedStatefulShellRoute<ScaffoldWithNavBarShellRoute>(branches: [
  // 首页
  TypedStatefulShellBranch<HomeBranchSellData>(routes: [TypedGoRoute<HomeRoute>(path: '/home')]),
  // 学习
  // TypedStatefulShellBranch<StudyBranchSellData>(routes: [TypedGoRoute<StudyRoute>(path: '/study')]),
  TypedStatefulShellBranch<CourseTableBranchSellData>(routes: [TypedGoRoute<CourseTableRoute>(path: '/courseTable')]),
  // 我的
  TypedStatefulShellBranch<UserBranchSellData>(routes: [TypedGoRoute<UserRoute>(path: '/user')]),
])
class ScaffoldWithNavBarShellRoute extends StatefulShellRouteData {
  const ScaffoldWithNavBarShellRoute();

  @override
  Page<void> pageBuilder(BuildContext context, GoRouterState state, StatefulNavigationShell navigationShell) {
    return NoTransitionPage(child: ScaffoldWithNavBar(navigationShell: navigationShell));
  }
}
