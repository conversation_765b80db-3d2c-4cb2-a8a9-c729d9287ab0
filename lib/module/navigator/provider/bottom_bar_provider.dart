import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/navigator/model/bottom_bar_model.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'bottom_bar_provider.g.dart';



BottomBarModel homeBarModel = BottomBarModel(id: 0, label: '首页', icon: 'tab_home_normal_icon.png', activeIcon: 'tab_home_select_icon.png', iconData: Icons.home, isActive: false, needLogin: false);
BottomBarModel studyBarModel = BottomBarModel(id: 1, label: '学习', icon: 'tab_study_normal_icon.png', activeIcon: 'tab_study_select_icon.png', iconData: Icons.book, isActive: false, needLogin: true);
BottomBarModel myCenterBarModel = BottomBarModel(id: 2, label: '我的', icon: 'tab_my_normal_icon.png', activeIcon: 'tab_my_select_icon.png', iconData: Icons.usb_rounded, isActive: false, needLogin: false);





@riverpod
class BottomBar extends _$BottomBar {
  @override
  List<BottomBarModel> build() {
    if (ref.read(loginProvider)) {
      studyBarModel = studyBarModel.copyWith(isActive: true);
      _buryingPoint(studyBarModel);
    } else {
      homeBarModel = homeBarModel.copyWith(isActive: true);
    }
    return [homeBarModel, studyBarModel, myCenterBarModel];
  }



  /// 切换底部导航栏
  void toggle(BottomBarModel model) {
    state = [
      for (final item in state)
        if (item.id == model.id) item.copyWith(isActive: true) else item.copyWith(isActive: false)
    ];
    _buryingPoint(model);
  }



  void _buryingPoint(BottomBarModel model){
    if(model.id == studyBarModel.id) {
      AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00002', moduleOri: '点击【学习】按钮');
    }
    if(model.id == homeBarModel.id) {
      AppBuryingPointUtil.click(modulePart: '178_cqjk_app_home_00001', moduleOri: '点击【首页】按钮');
    }
    if(model.id == myCenterBarModel.id) {
      AppBuryingPointUtil.click(modulePart: '178_cqjk_app_mine_00001', moduleOri: '点击【我的】页面底部导航按钮');
    }

  }
}
