import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/course_table/provider/course_table_my_provider.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart' as login_routes;
import 'package:changqing_health_app/module/navigator/model/bottom_bar_model.dart';
import 'package:changqing_health_app/module/navigator/provider/bottom_bar_provider.dart';
import 'package:changqing_health_app/module/study/provider/study_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

/// author: Liusilong
/// date: 2024/3/20
/// desc:
//

class BottomNavigationBarWidget extends ConsumerStatefulWidget {
  final ValueChanged<int>? tapChanged;

  const BottomNavigationBarWidget({super.key, this.tapChanged});

  @override
  ConsumerState createState() => _BottomNavigationBarWidgetState();
}

class _BottomNavigationBarWidgetState extends ConsumerState<BottomNavigationBarWidget> {
  double? _paddingBottom;

  @override
  Widget build(BuildContext context) {
    _paddingBottom ??= MediaQuery.of(context).padding.bottom;
    final bottomBarList = ref.watch(bottomBarProvider);
    return Column(
      children: [
        Container(
          height: 0.5,
          color: Color(0x80CCCCCC),
        ),
        Container(
          color: Colors.white,
          height: 100.w + _paddingBottom!,
          padding: EdgeInsets.only(bottom: _paddingBottom!),
          child: Row(children: bottomBarList.map((value) => _buildItemWidget(value)).toList()),
        ),
      ],
    );
  }

  Widget _buildItemWidget(BottomBarModel model) {
    return Expanded(
      child: Container(
        color: Colors.transparent,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/images/tab/${model.isActive ? model.activeIcon : model.icon}', width: 48.w),
            SizedBox(height: 4.w),
            Text(model.label, style: TextStyle(fontSize: 24.sp, color: model.isActive ? Color(0xFF333333) : Color(0xFF999999), fontWeight: FontWeight.bold))
          ],
        ),
      ).onTap(() {
        _onTap(model);
      }, tapInterval: 300),
    );
  }

  /// 点击事件
  void _onTap(BottomBarModel model) {
    if (model.needLogin) {
      final isLogin = ref.read(loginProvider);
      if (!isLogin) {
        context.push(ref.read(login_routes.getLoginRouteProvider));
        return;
      }
    }

    if (model.id == 1) {
      if (courseTableLiveIsLoad) {
        ref.read(courseTableMyProvider.notifier).getCourseTableMyDataList();
      }
    }
    ref.read(bottomBarProvider.notifier).toggle(model);
    if (widget.tapChanged != null) {
      widget.tapChanged!(model.id);
    }
  }
}
