// author: <PERSON><PERSON><PERSON>
// date: 2024/12/12
// desc: 带导航栏的Scaffold

import 'package:changqing_health_app/app/init.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/navigator/provider/bottom_bar_provider.dart';
import 'package:changqing_health_app/module/navigator/widget/bottom_navigation_bar_widget.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/widget/route_aware_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:app_links/app_links.dart';
import 'package:changqing_health_app/module/h5liveroom/util/h5_scheme_manager.dart';

class ScaffoldWithNavBar extends ConsumerStatefulWidget {
  final StatefulNavigationShell navigationShell;
  const ScaffoldWithNavBar({super.key, required this.navigationShell});

  @override
  ConsumerState<ScaffoldWithNavBar> createState() => _ScaffoldWithNavBarState();
}

class _ScaffoldWithNavBarState extends ConsumerState<ScaffoldWithNavBar> {


@override
  void initState() {
    super.initState();
        WidgetsBinding.instance.addPostFrameCallback((_) {
      _initDeepLinks();
    });
  }

    void _initDeepLinks() {
    AppLinks().uriLinkStream.listen((url) async {
      ref.read(schemeUriProvider.notifier).state = url;
      debugPrint('onAppLink = $url');
      await Future.delayed(Duration(seconds: 1));
      H5SchemeManager.handleHScheme(url, ref);
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.listen(loginProvider, (previous, next) {
      if (next) {
        ref.read(bottomBarProvider.notifier).toggle(homeBarModel);
        widget.navigationShell.goBranch(homeBarModel.id);
      }
    });
    return Scaffold(
      resizeToAvoidBottomInset: false,
      // drawer: SessionListWidget(),
      drawerEnableOpenDragGesture: false,
      body: RouteAwareWidget(
        // didPopNext: () => resetSystemUIStyle(),
        child: Column(
          children: [
            Expanded(child: widget.navigationShell),
            BottomNavigationBarWidget(tapChanged: (index) {
              widget.navigationShell.goBranch(index);
            }),
          ],
        ),
      ),
    );
  }
}
