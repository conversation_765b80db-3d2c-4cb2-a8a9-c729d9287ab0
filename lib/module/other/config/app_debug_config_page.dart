import 'package:changqing_health_app/config/enum/env_enum.dart';
import 'package:changqing_health_app/config/env_config.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/provider/app_provider.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

part 'app_debug_config_page.g.dart';

@TypedGoRoute<AppDebugConfigRoute>(path: '/app_debug_config')
class AppDebugConfigRoute extends GoRouteData {
  const AppDebugConfigRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: AppDebugConfigPage());
  }
}

class AppDebugConfigPage extends ConsumerStatefulWidget {
  const AppDebugConfigPage({super.key});

  @override
  ConsumerState<AppDebugConfigPage> createState() => _AppDebugConfigPageState();
}

class _AppDebugConfigPageState extends ConsumerState<AppDebugConfigPage> {
  void _setProxy() async {
    await showCupertinoInputDialog(context);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("调试配置")),
      body: SingleChildScrollView(
        child: Column(
          children: [
            24.gap,
            _appWidget(),
            24.gap,
            _buildProxyWidget(),
          ],
        ),
      ),
    );
  }

  /// app信息
  Widget _appWidget() {
    return SizedBox(
      width: double.maxFinite,
      child: Container(
        margin: EdgeInsets.symmetric(vertical: 24.w, horizontal: 24.w),
        padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 24.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('App信息', style: TextStyle(color: Colors.black, fontSize: 32.sp, fontWeight: FontWeight.w700)),
            12.gap,
            Text('当前环境: ${EnvConfig().env == Env.dev ? "测试环境" : "正式环境"}', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            Text('当前渠道: ${EnvConfig().channel}', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            Text('当前版本号: ${ref.read(deviceInfoProvider).value?.version}+${ref.read(deviceInfoProvider).value?.buildNumber}',
                style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            Text('当前App域名:\n ${EnvConfig().appBaseUrl}', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            Text('当前直播间鉴权域名:\n ${EnvConfig().zhongtaiDomain}', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            Text('当前直播间红包相关域名:\n ${EnvConfig().dvDomain}', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            Text('当前直播间体验营相关域名:\n ${EnvConfig().zhongtaiAppDomain}', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
          ],
        ),
      ),
    );
  }

  /// 抓包信息
  Widget _buildProxyWidget() {
    String proxy = SpUtil().get(spAppNetworkProxy) ?? '无';
    if (proxy.isEmpty) {
      proxy = '无';
    }
    return GestureDetector(
      onTap: _setProxy,
      child: SizedBox(
        width: double.maxFinite,
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 24.w, horizontal: 24.w),
          padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 24.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('代理设置', style: TextStyle(color: Colors.black, fontSize: 32.sp, fontWeight: FontWeight.w700)),
              12.gap,
              Text('当前代理信息: $proxy', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
              24.gap,
              Text('请注意，更改完代理后，需要后台杀死App后，重新打开App才可生效', style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 24.sp)),
            ],
          ),
        ),
      ),
    );
  }
}

Future showCupertinoInputDialog(BuildContext context) async {
  String proxy = SpUtil().get(spAppNetworkProxy) ?? '';
  final TextEditingController textController = TextEditingController();
  textController.text = proxy;
  return showCupertinoDialog(
    context: context,
    builder: (BuildContext context) {
      return CupertinoAlertDialog(
        title: Text('设置代理'),
        content: Column(
          children: [
            SizedBox(height: 12),
            CupertinoTextField(
              controller: textController,
              placeholder: '192.168.1.10:8888',
              padding: EdgeInsets.symmetric(vertical: 12, horizontal: 10),
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[\d:.]'))],
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('取消'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
          CupertinoDialogAction(
            child: Text('确定'),
            onPressed: () {
              String input = textController.text;
              SpUtil().set(spAppNetworkProxy, input);
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}
