import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


class ZhongTaiData {
  final String? token;
  final String? uid;
  final String? mid;
  final String? jwt;
  final int? expire;
  final String? pub;

  ZhongTaiData({
    this.token,
    this.uid,
    this.mid,
    this.jwt,
    this.expire,
    this.pub,
  });

  factory ZhongTaiData.fromJson(Map<String, dynamic> json) {
    return ZhongTaiData(
      token: json['token'],
      uid: json['uid'],
      mid: json['mid'],
      jwt: json['jwt'],
      expire: json['expire'],
      pub: json['pub'],
    );
  }
}
