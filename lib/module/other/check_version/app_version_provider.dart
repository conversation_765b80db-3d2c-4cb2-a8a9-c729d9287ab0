import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:changqing_health_app/service/http/response_model.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:json_annotation/json_annotation.dart';
part 'app_version_provider.g.dart';

@Riverpod(keepAlive: true)
class AppVersionProvider extends _$AppVersionProvider {
  @override
  Future<PackageInfo> build() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo;
  }

  VersionInfo? _versionInfo;
  VersionInfo? get versionInfo => _versionInfo;

  bool get isForceUpdate => _versionInfo?.forceUpdate == true && isNewVersion;

  bool get isReview => _versionInfo?.reviewVersion == state.value?.version;

  bool get isNewVersion => _compareVersions(_versionInfo?.newVersion ?? '', state.value?.version ?? '') > 0;

  int _compareVersions(String versionA, String versionB) {
    try {
      List<int> aParts = versionA.split('.').map(int.parse).toList();
      List<int> bParts = versionB.split('.').map(int.parse).toList();

      // 补全长度为 3（主版本号.次版本号.修订号）
      while (aParts.length < 3) {
        aParts.add(0);
      }
      while (bParts.length < 3) {
        bParts.add(0);
      }

      for (int i = 0; i < 3; i++) {
        if (aParts[i] > bParts[i]) return 1;
        if (aParts[i] < bParts[i]) return -1;
      }
      return 0;
    } catch (_) {
      return 0;
    }
  }

  Future<void> checkVersion() async {
    try {
      ResponseModel resModel = await ApiClient.get("/api/cqapp/front/app/version/check_v2");
      if (resModel.code == 0) {
        final versionInfo = VersionInfo.fromJson(resModel.data);
        _versionInfo = versionInfo;
      }
    } catch (_) {}
    // _versionInfo = VersionInfo.fromJson(
    //   {
    //     "reviewVersion": "1.0.1",
    //     "newVersion": "1.0.1",
    //     "forceUpdate": true,
    //     "updateContent": "更新内容",
    //     "updateUrl": "https://www.baidu.com",
    //   },
    // );
  }
}

@JsonSerializable()
class VersionInfo {
  final String reviewVersion;
  final String? newVersion;
  final bool? forceUpdate;
  final String? updateContent;
  final String? updateUrl;

  VersionInfo({required this.reviewVersion, required this.newVersion, required this.forceUpdate, required this.updateContent, required this.updateUrl});

  factory VersionInfo.fromJson(Map<String, dynamic> json) => _$VersionInfoFromJson(json);

  Map<String, dynamic> toJson() => _$VersionInfoToJson(this);
}
