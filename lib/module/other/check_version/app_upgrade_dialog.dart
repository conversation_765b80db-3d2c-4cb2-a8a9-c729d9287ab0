import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';

class AppUpgradeDialog extends ConsumerWidget {
  const AppUpgradeDialog({super.key});

  static Future<void> show(BuildContext context, WidgetRef ref) async {
    final versionInfo = ref.read(appVersionProviderProvider.notifier).versionInfo!;

    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text('发现新版本'),
          content: Text('新版本:${versionInfo.newVersion}\n${versionInfo.updateContent}'),
          actions: <CupertinoDialogAction>[
            if (versionInfo.forceUpdate == false)
              CupertinoDialogAction(
                child: Text('暂不更新'),
                onPressed: () {
                  Navigator.of(context).pop(); // 关闭对话框
              },
            ),
            CupertinoDialogAction(
              child: Text('立即更新'),
              isDestructiveAction: true,
              onPressed: () {
                // 使用外部浏览器打开下载链接
                launchUrl(Uri.parse(versionInfo.updateUrl!), mode: LaunchMode.externalApplication);
                if (versionInfo.forceUpdate != true) {
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        );
      },
    );
    // return showDialog<void>(
    //   context: context,
    //   builder: (context) => const AppUpgradeDialog(),
    // );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Placeholder();
  }
}
