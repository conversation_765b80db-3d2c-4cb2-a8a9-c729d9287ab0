import 'package:changqing_health_app/enum/cq_class_package_enum.dart';
import 'package:changqing_health_app/module/course_table/page/course_class_package_detail_page.dart';
import 'package:changqing_health_app/module/course_table/page/course_table_tab_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';

part 'course_table_route.g.dart';

/// 课表tab路由
class CourseTableBranchSellData extends StatefulShellBranchData {
  const CourseTableBranchSellData();
}

/// 课表tab路由
// @TypedGoRoute<CourseTableRoute>(path: '/courseTable')
class CourseTableRoute extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CourseTableRefreshDataPage();
  }
}

/// 课包--->课表数据
@TypedGoRoute<CourseTableClassPackageDetailRoute>(path: '/courseTable/classPackageDetail')
class CourseTableClassPackageDetailRoute extends GoRouteData {
  final int packageType;
  final String packageTitle;
  final String? seriesTitle;
  final String? seriesType;

  CourseTableClassPackageDetailRoute({required this.packageType, required this.packageTitle,  this.seriesType,  this.seriesTitle});

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return CupertinoPage(child: CourseClassPackageDetailPage(packageType: packageType, packageTitle: packageTitle, seriesType: seriesType, seriesTitle: seriesTitle));
  }
}
