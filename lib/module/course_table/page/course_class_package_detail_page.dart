import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/enum/cq_class_package_enum.dart';
import 'package:changqing_health_app/enum/cq_live_enum.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/course_table/provider/course_class_package_detail_provider.dart';
import 'package:changqing_health_app/module/course_table/route/course_table_route.dart';
import 'package:changqing_health_app/module/course_table/widget/course_live_status_widget.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:changqing_health_app/widget/cq_smart_refresher_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 课表---我的课程---详情页面
class CourseClassPackageDetailPage extends ConsumerStatefulWidget {
  final int packageType;
  final String packageTitle;
  final String? seriesTitle;
  final String? seriesType;

  const CourseClassPackageDetailPage({super.key, required this.packageType, required this.packageTitle, this.seriesType, this.seriesTitle});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CourseClassPackageDetailPageState();
}

class _CourseClassPackageDetailPageState extends ConsumerState<CourseClassPackageDetailPage> {
  @override
  void initState() {
    _buryingPoint();
    super.initState();
  }

  /// 点击体验营的金刚位
  void _clickType(CourseClassPackageDetailTypeModel model) {
    CourseTableClassPackageDetailRoute(packageType: widget.packageType, packageTitle: widget.packageTitle, seriesType: model.type, seriesTitle: model.name).push(context);
    AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00009', moduleOri: '学习-课程卡片-陪伴营直播列表-点击课程系列icon', alternate: {"pby_type" : widget.seriesTitle ?? ''});
  }

  Future<List<CourseTableLiveItemModel>> _onFetch(int pageNo, int pageSize) async {
    if (widget.packageType == classPackageCompanionCamp && widget.seriesType == null) {
      /// 体验营需要有顶部的金刚位
      await ref.read(courseClassPackageTypeProvider.future);
    }
    dynamic result = await ref.read(getClassDetailListDataProvider(packageType: widget.packageType, seriesType: widget.seriesType, page: pageNo, pageSize: pageSize).future);
    return result;
  }

  void _buryingPoint() {
    AppBuryingPointUtil.show(modulePart: '178_cqjk_app_xuexi_00006', moduleOri: '学习-课程卡片-访问直播列表', alternate: {"kc_type": widget.packageTitle, "pby_type": widget.seriesTitle ?? ''});
  }

  void _clickBuryingPoint(CourseTableLiveItemModel model){
    if(model.liveStatus == LiveStatus.live) {
      AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00007', moduleOri: '学习-课程卡片-直播列表-点击【观看直播】按钮', alternate: {"kc_type": widget.packageTitle, "pby_type": widget.seriesTitle ?? ''});
    }else if(model.liveStatus == LiveStatus.generated) {
      AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00008', moduleOri: '学习-课程卡片-直播列表-点击【观看回放】按钮', alternate: {"kc_type": widget.packageTitle, "pby_type": widget.seriesTitle ?? ''});
    }

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.seriesTitle ?? widget.packageTitle)),
      body: CQSmartRefresherWidget(
        headerWidget: _headerWidget,
        onFetch: _onFetch,
        itemBuilder: (context, value, index) {
          return Padding(
            padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 24.w, top: index == 0 ? 24.w : 0),
            child: CourseLiveStatusWidget(model: value, buryingCallBack: ()=> _clickBuryingPoint(value)),
          );
        },
      ),
    );
  }

  Widget? _headerWidget(BuildContext context) {
    if (widget.packageType != classPackageCompanionCamp) {
      return null;
    }
    // 判断金刚位是否有值
    if (widget.seriesType != null || ref.read(courseClassPackageTypeProvider).value == null) {
      return null;
    }
    double itemWidth = (1.sw - 24.w * 2 - 10.w * 2 - 2) / 3;
    return Padding(
      padding: EdgeInsets.only(left: 24.w, top: 24.w, bottom: 24.w, right: 24.w),
      child: Wrap(
        spacing: 10.w,
        runSpacing: 10.w,
        children: ref.read(courseClassPackageTypeProvider).value!.map((value) {
          return Container(
            color: Colors.transparent,
            width: itemWidth,
            child: Column(
              children: [
                SizedBox(height: 24.w),
                CQCachedNetworkImage(imageUrl: value.icon!, width: 110.w, height: 110.w),
                SizedBox(height: 16.w),
                Text(value.name ?? '', style: TextStyle(color: Color(0xFF333333), fontWeight: FontWeight.bold, fontSize: 30.sp), textAlign: TextAlign.center),
              ],
            ),
          ).onTap(() {
            _clickType(value);
          });
        }).toList(),
      ),
    );
  }
}
