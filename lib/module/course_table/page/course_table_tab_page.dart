import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_tab_item_model.dart';
import 'package:changqing_health_app/module/course_table/page/course_table_class_package_page.dart';
import 'package:changqing_health_app/module/course_table/provider/course_table_tab_provider.dart';
import 'package:changqing_health_app/module/course_table/widget/custom_tabbar_indicator.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/study/page/study_page.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/webview/page/tab_web_view_widget.dart';
import 'package:changqing_health_app/widget/cq_animation_tabbar.dart';
import 'package:changqing_health_app/widget/cq_loading_widget.dart';
import 'package:changqing_health_app/widget/keep_alive_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 课表主页面
class CourseTableRefreshDataPage extends ConsumerStatefulWidget {
  const CourseTableRefreshDataPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CourseTableRefreshDataPageState();
}

class _CourseTableRefreshDataPageState extends ConsumerState<CourseTableRefreshDataPage> {
  ProviderSubscription<bool>? _loginSubscription;
  GlobalKey _globalKey = GlobalKey();

  @override
  void initState() {
    // 监听重新登录事件
    _loginSubscription?.close();
    _loginSubscription = null;
    _loginSubscription = ref.listenManual(loginProvider, (previous, next) async {
      if (next == true) {
        // 重新请求课表数据
        _globalKey = GlobalKey();
        // ref.read(courseTableTabProvider.notifier).refreshCourseTableTabList();
        // ref.invalidate(courseTableTabProvider);
      }
    });
    super.initState();
    try{
      AppBuryingPointUtil.show(modulePart: '178_cqjk_app_xuexi_00001', moduleOri: '访问学习页面【我的课程】');
    }catch(_){}
  }

  @override
  void dispose() {
    _loginSubscription?.close();
    _loginSubscription = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (ref.read(appVersionProviderProvider.notifier).isReview) {
      return StudyPage();
    }

    final login = ref.watch(loginProvider);
    if (login == false) {
      return SizedBox.shrink();
    }
    final request = ref.watch(courseTableTabProvider);
    return request.when(
      data: (data) {
        return CourseTableTabPage(labelList: data, key: _globalKey);
      },
      error: (error, stack) => Text(error.toString()),
      loading: () => const Center(child: CQLoadingWidget()),
    );
  }
}

/// 课表主页面
class CourseTableTabPage extends ConsumerStatefulWidget {
  final List<CourseTableTabItemModel> labelList;

  const CourseTableTabPage({super.key, required this.labelList});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CourseTableTabPageState();
}

class _CourseTableTabPageState extends ConsumerState<CourseTableTabPage> with TickerProviderStateMixin {
  TabController? _tabCtrl;
  List<CourseTableTabItemModel>? _labelList;

  @override
  void initState() {
    super.initState();
    _labelList = widget.labelList;
    _initTabController();
  }

  void _initTabController() {
    if (_tabCtrl != null) {
      _disposeTabCtrl();
    }
    _tabCtrl = TabController(length: _labelList!.length, vsync: this);
    _tabCtrl!.animation?.addListener(_setState);
    _tabCtrl!.addListener(_setState);
  }

  void _disposeTabCtrl() {
    _tabCtrl!.animation?.removeListener(_setState);
    _tabCtrl!.removeListener(_setState);
    _tabCtrl!.dispose();
    _tabCtrl = null;
  }

  void _setState() {
    if(!_tabCtrl!.indexIsChanging) {
      _buryingPoint();
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _disposeTabCtrl();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: Container(
          color: Colors.transparent,
          width: context.sw,
          height: kToolbarHeight,
          alignment: Alignment.centerLeft,
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              physics: NeverScrollableScrollPhysics(),
              controller: _tabCtrl,
              children: _labelList!.map((e) => KeepAliveWidget(child: _buildTabViewItem(e))).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      width: context.sw,
      color: Colors.transparent,
      child: TabBar(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        dividerColor: const Color(0xffEEEEEE),
        dividerHeight: 0.5.w,
        tabAlignment: TabAlignment.start,
        isScrollable: true,
        controller: _tabCtrl,
        splashFactory: NoSplash.splashFactory,
        labelPadding: EdgeInsets.only(right: 24.w),
        indicatorSize: TabBarIndicatorSize.label,
        tabs: _buildTabBarItemList(),
        indicator: CustomIndicator(
          color: Color(0xFF27B46B),
          paintingStyle: PaintingStyle.fill,
          bottomLeftRadius: 2,
          bottomRightRadius: 2,
          topLeftRadius: 2,
          topRightRadius: 2,
          horizontalPadding: 40.w,
          verticalPadding: 8.w,
          height: 6.w,
        ),
      ),
    );
  }

  List<Widget> _buildTabBarItemList() {
    List<Widget> list = [];
    for (int i = 0; i < _labelList!.length; i++) {
      Tab tab = Tab(
        child: CqAnimationTabBar(
          controller: _tabCtrl!,
          index: i,
          label: _labelList![i].title!,
          selectedColor: const Color(0xFF333333),
          unselectedColor: const Color(0xFF6C6C6C),
          fontSize: 32.sp,
        ),
      );
      list.add(tab);
    }
    return list;
  }

  Widget _buildTabViewItem(CourseTableTabItemModel item) {
    if (item.tabEnum == CourseTabEnum.course) {
      return CourseTableClassPackagePage();
    } else {
      return TabWebViewWidget(url: item.value!);
    }
  }

  void _buryingPoint(){
    AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00003', moduleOri: '点击tab', alternate: {"tab_type" : _labelList![_tabCtrl!.index].title});
  }
}
