import 'package:cached_network_image/cached_network_image.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/enum/cq_class_package_enum.dart';
import 'package:changqing_health_app/enum/cq_live_enum.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/course_table/route/course_table_route.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_my_data_model.dart';
import 'package:changqing_health_app/module/course_table/provider/course_push_live_provider.dart';
import 'package:changqing_health_app/module/course_table/provider/course_table_my_provider.dart';
import 'package:changqing_health_app/module/course_table/widget/course_live_status_widget.dart';
import 'package:changqing_health_app/module/course_table/widget/live_status_button.dart';
import 'package:changqing_health_app/module/h5liveroom/util/h5_scheme_manager.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:changqing_health_app/widget/cq_align_text.dart';
import 'package:changqing_health_app/widget/cq_loading_widget.dart';
import 'package:changqing_health_app/widget/cq_smart_refresher_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 课表---我的课程
class CourseTableClassPackagePage extends ConsumerStatefulWidget {
  const CourseTableClassPackagePage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _CourseTableClassPackagePageState();
}

class _CourseTableClassPackagePageState extends ConsumerState<CourseTableClassPackagePage> {
  Future<List> _onFetch(_, __) async {
    return await _initData();
  }

  Future<List> _initData() async {
    await ref.read(courseTableMyProvider.notifier).getCourseTableMyDataList();
    return [1];
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(courseTableMyProvider);
    return Container(
      color: Color(0xFFF2F4F7),
      child: CQSmartRefresherWidget(
        onFetch: _onFetch,
        bodyWidget: _bodyWidget(),
        enablePullUp: false,
      ),
    );
  }

  Widget _bodyWidget() {
    if (ref.read(courseTableMyProvider.notifier).isEmptyData()) {
      return _noDataWidget();
    }
    final data = ref.watch(courseTableMyProvider).value!;
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          _liveWidget(data.liveList),
          SizedBox(height: 24.w),
          _classPackageWidget(data.packageList),
        ],
      ),
    );
  }

  // 今日直播
  Widget _liveWidget(List<CourseTableLiveItemModel>? liveList) {
    if (liveList?.isEmpty ?? true) {
      return SizedBox.shrink();
    }
    return _sectionWidget(
        '今日直播',
        'course_tab_live_icon.png',
        topPadding: 48.w,
        liveList!.map((e) {
          return Padding(
            padding: EdgeInsets.only(top: 24.w),
            child: CourseLiveStatusWidget(model: e, notStartedUseBigStyle: true, buryingCallBack: () => _buryingPoint(e)),
          );
        }).toList());
  }

  // 课包
  Widget _classPackageWidget(List<CourseTableClassPackageModel>? classList) {
    if (classList?.isEmpty ?? true) {
      return SizedBox.shrink();
    }
    return _sectionWidget(
      '我的课程',
      'course_list_icon.png',
      topPadding: 48.w,
      bottomPadding: 24.w,
      classList!.map((e) {
        return Padding(
          padding: EdgeInsets.only(top: 24.w),
          child: CourseClassPackageWidget(model: e),
        );
      }).toList(),
    );
  }

  Widget _sectionWidget(String title, String imagePath, List<Widget> children, {required double topPadding, double bottomPadding = 0}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: topPadding),
        Row(
          children: [
            Image.asset('assets/images/course/$imagePath', width: 40.w),
            SizedBox(width: 12.w),
            CqAlignText(title, fontSize: 36.sp, color: Color(0xFF333333), fontWeight: FontWeight.bold),
          ],
        ),
        ...children,
        SizedBox(height: bottomPadding),
      ],
    );
  }

  Widget _noDataWidget() {
    return Container(
      alignment: Alignment.center,
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/course/course_empty_icon.png',
            width: 460.w,
            height: 254.w,
          ),
          SizedBox(height: 20.w),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 32.sp,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  void _buryingPoint(CourseTableLiveItemModel model) {
    if (model.liveStatus == LiveStatus.live) {
      AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00004', moduleOri: '今日直播-点击【观看直播】按钮');
    }
  }
}

class CourseClassPackageWidget extends StatelessWidget {
  final CourseTableClassPackageModel model;

  const CourseClassPackageWidget({super.key, required this.model});

  void _pushToDetail(BuildContext context) {
    try {
      _buryingPoint();
    } catch (_) {}
    if (model.packageType == classPackageExperienceCamp) {
      if (model.liveUrl?.isEmpty ?? true) {
        return;
      }
      H5SchemeManager.openLiveUrlOnNewPage(model.liveUrl!, title: model.title);
      return;
    }
    CourseTableClassPackageDetailRoute(packageType: model.packageType!, packageTitle: model.title ?? '').push(context);
  }

  void _buryingPoint() {
    AppBuryingPointUtil.click(modulePart: '178_cqjk_app_xuexi_00005', moduleOri: '学习-课程卡片-点击【去学习】按钮', alternate: {"kc_type": model.title});
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        CQCachedNetworkImage(imageUrl: model.bgCover ?? '', width: 702.w, height: 252.w, fit: BoxFit.fitWidth),
        Positioned(
          left: 32.w,
          right: 240.w,
          top: 50.w,
          bottom: 50.w,
          child: Container(
            color: Colors.transparent,
            alignment: Alignment.centerLeft,
            child: Text(model.title ?? '', style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.bold, color: Color(0xFF333333))),
          ),
        ),
      ],
    ).onTap(() {
      _pushToDetail(context);
    });
  }
}
