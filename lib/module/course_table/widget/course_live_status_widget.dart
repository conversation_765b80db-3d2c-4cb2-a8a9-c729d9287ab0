import 'package:changqing_health_app/enum/cq_live_enum.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/course_table/provider/course_push_live_provider.dart';
import 'package:changqing_health_app/module/course_table/widget/live_status_button.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:changqing_health_app/util/date_time_util.dart';
import 'package:changqing_health_app/widget/cq_align_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 每个课包下的课表 列表的item
class CourseLiveStatusWidget extends ConsumerStatefulWidget {
  final CourseTableLiveItemModel model;
  // 未开播的卡片，是否使用大的风格。和直播中的一致
  final bool notStartedUseBigStyle;
  final VoidCallback? buryingCallBack;

  const CourseLiveStatusWidget({super.key, required this.model, this.notStartedUseBigStyle = false, this.buryingCallBack});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<CourseLiveStatusWidget> {

  void _clickItem() {
    CoursePushLiveProvider.pushLivePage(widget.model);
    widget.buryingCallBack?.call();
  }

  @override
  Widget build(BuildContext context) {
    return _buildBodyWidget().onTap(() {
      _clickItem();
    });
  }
  Widget _buildBodyWidget() {
    if (widget.model.liveStatus == LiveStatus.live || (widget.model.liveStatus == LiveStatus.notStarted && widget.notStartedUseBigStyle)) {
      return _buildLiveWidget();
    }
    return _buildPlaybackWidget();
  }

  /// 直播item
  Widget _buildLiveWidget() {
    return AspectRatio(
      aspectRatio: 702 / 395,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24.w),
        child: Stack(
          children: [
            Image.network(widget.model.liveCover!, width: double.maxFinite, height: double.maxFinite, fit: BoxFit.cover),
            _liveTipsWidget(),
            Positioned.fill(
              child: FractionallySizedBox(
                alignment: Alignment.bottomCenter,
                heightFactor: 0.5,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter, // 渐变起点（顶部）
                      end: Alignment.bottomCenter, // 渐变终点（底部）
                      colors: [
                        Color(0x00000000), // 顶部颜色
                        Color(0x99000000), // 底部颜色
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 25.w,
              right: 24.w,
              left: 24.w,
              child: Row(
                children: [
                  _timeWidget(),
                  Expanded(child: SizedBox.shrink()),
                  LiveStatusButton(status: widget.model.liveStatus!, notStartedUseBigStyle: widget.notStartedUseBigStyle),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 直播中、未开始 左上角的 tips
  Widget _liveTipsWidget() {
    if (widget.model.liveStatus == LiveStatus.notStarted) {
      return Positioned(
        left: 0,
        top: 0,
        width: 132.w,
        height: 52.w,
        child: Container(
          decoration: BoxDecoration(
            color: Color(0xFFD2D2D2),
            borderRadius: BorderRadius.only(bottomRight: Radius.circular(24.w)),
          ),
          alignment: Alignment.center,
          child: CqAlignText('未开始', fontSize: 28.sp, color: Color(0xFFFFFFFF), fontWeight: FontWeight.bold),
        ),
      );
    }
    return Positioned(
      left: 0,
      top: 0,
      width: 158.w,
      height: 52.w,
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFF2324),
          borderRadius: BorderRadius.only(bottomRight: Radius.circular(24.w)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/images/course/course_live_icon.png', width: 20.w, height: 20.w),
            SizedBox(width: 6.w),
            CqAlignText('直播中', fontSize: 28.sp, color: Colors.white, fontWeight: FontWeight.bold),
          ],
        ),
      ),
    );
  }

  /// 回放item
  Widget _buildPlaybackWidget() {
    return Container(
      width: 702.w,
      padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24.w),
      ),
      child: Row(
        children: [
          _buildPlaybackCover(),
          _buildPlaybackInfo(),
        ],
      ),
    );
  }

  Widget _buildPlaybackInfo() {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(right: 24.w),
        height: 210.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                style: TextStyle(fontSize: 32.sp, color: Color(0xff333333), fontWeight: FontWeight.bold),
                children: [
                  if(widget.model.liveTag != null)WidgetSpan(
                    child: Container(
                      margin: EdgeInsets.only(right: 12.w),
                      decoration: BoxDecoration(color: Color(0xFFFDEDC5), borderRadius: BorderRadius.circular(4.w)),
                      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.w),
                      child: CqAlignText(widget.model.liveTag!, fontSize: 24.sp, fontWeight: FontWeight.bold, color: Color(0xFF9F4A00)),
                    ),
                  ),
                  TextSpan(text: widget.model.liveTitle!),
                ],
              ),
            ),
            Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Text(
                //   '观看至20%',
                //   style: TextStyle(fontSize: 26.sp, color: Color(0xff999999)),
                // ),
                Expanded(child: SizedBox.shrink()),
                LiveStatusButton(status: widget.model.liveStatus!, notStartedUseBigStyle: widget.notStartedUseBigStyle),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildPlaybackCover() {
    return Container(
      margin: EdgeInsets.only(left: 24.w, right: 16.w),
      width: 280.w,
      height: 210.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: CQCachedNetworkImage(
              imageUrl: widget.model.liveCover!,
              height: 210.w,
              width: 280.w,
              fit: BoxFit.cover,
            ),
          ),
          Positioned(
            left: 0,
            bottom: 0,
            child: _buildPlaybackDate(),
          )
        ],
      ),
    );
  }

  Widget _buildPlaybackDate() {
    return Container(
      width: 280.w,
      height: 46.w,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.5),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(12.r),
          bottomRight: Radius.circular(12.r),
        ),
      ),
      child: _timeWidget(),
    );
  }

  Widget _timeWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(
          'assets/images/study/icon_play_time.png',
          width: 24.w,
          height: 24.w,
        ),
        8.gap,
        Text(
          widget.model.liveTime!.isEmpty ? '' : DateTimeUtil.format(time: widget.model.liveTime!),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(fontSize: widget.notStartedUseBigStyle ? 32.sp : 26.sp, color: Colors.white),
        ),
      ],
    );
  }
}
