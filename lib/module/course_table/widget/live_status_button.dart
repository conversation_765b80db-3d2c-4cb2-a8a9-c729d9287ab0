import 'package:changqing_health_app/enum/cq_live_enum.dart';
import 'package:changqing_health_app/widget/cq_align_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 直播状态按钮
class LiveStatusButton extends ConsumerStatefulWidget {
  final LiveStatus status;

  // 未开播的卡片，是否使用大的风格。和直播中的一致
  final bool notStartedUseBigStyle;

  const LiveStatusButton({super.key, required this.status, this.notStartedUseBigStyle = false});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<LiveStatusButton> {
  @override
  Widget build(BuildContext context) {
    late Color bgColor;
    late Color textColor;
    Color? layerColor;
    late String title;
    double width = 188.w;
    double height = 52.w;

    switch (widget.status) {
      case LiveStatus.notStarted:
        textColor = Color(0xFF999999);
        bgColor = Color(0xFFF5F5F5);
        title = '直播未开始';
        if (widget.notStartedUseBigStyle) {
          width = 204.w;
          height = 60.w;
        }

        break;
      case LiveStatus.live:
        textColor = Color(0xFFFFFFFF);
        bgColor = Color(0xFF23C46E);
        title = '观看直播';
        width = 204.w;
        height = 60.w;
        break;
      case LiveStatus.generating:
        textColor = Color(0xFF23C46E);
        bgColor = Color(0xFFE9F9F0);
        layerColor = Color(0x8023C46E);
        title = '回放生成中';
        break;
      case LiveStatus.generated:
        textColor = Color(0xFF23C46E);
        bgColor = Color(0xFFE9F9F0);
        title = '观看回放';
        break;
    }

    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(height / 2),
        border: layerColor == null ? null : Border.all(color: layerColor, width: 1),
      ),
      child: CqAlignText(
        title,
        fontSize: 28.sp,
        color: textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }
}
