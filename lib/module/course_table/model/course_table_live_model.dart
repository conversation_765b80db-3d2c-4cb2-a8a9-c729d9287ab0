import 'package:changqing_health_app/enum/cq_live_enum.dart';

/// 陪伴营 课表 分类
class CourseClassPackageDetailTypeModel {
  String? type;
  String? name;
  String? icon;

  CourseClassPackageDetailTypeModel({this.type, this.name, this.icon});

  CourseClassPackageDetailTypeModel.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    name = json['name'];
    icon = json['icon'];
  }
}

/// 课表itemModel
class CourseTableLiveItemModel {
  // 私域状态 0：直播未开始，1；直播中 2已结束，录播生成中 3：回放已生成，转码中 10：回放已生成
  // 修复营状态 0：未开始 1：直播中 2：回放生成中 3：回放已生成
  // 0：未开始 1：直播中 2：回放生成中 3：回放已生成
  final LiveStatus? liveStatus;
  final String? liveCover;
  final String? liveTitle;
  final String? liveTime;
  final String? liveUrl;
  final String? roomId;
  final int? videoId;
  final int? appId;
  final String? businessTitle;

  // 1修复营课 2私域后台课
  final String? businessType;

  final List? tags;


  String? get liveTag {
    try {
      if (tags == null || tags!.isEmpty) return null;
      return tags!.first;
    } catch (_) {}
    return null;
  }

  CourseTableLiveItemModel({
    required this.liveStatus,
    required this.liveCover,
    required this.liveTitle,
    required this.liveTime,
    required this.liveUrl,
    required this.roomId,
    required this.videoId,
    required this.businessTitle,
    required this.businessType,
    required this.appId,
    this.tags,
  });

  factory CourseTableLiveItemModel.fromJson(Map<String, dynamic> json) {
    LiveStatus? liveStatus = LiveStatus.notStarted;
    if (json['liveStatus'] == 0) {
      liveStatus = LiveStatus.notStarted;
    } else if (json['liveStatus'] == 1) {
      liveStatus = LiveStatus.live;
    } else if (json['liveStatus'] == 2) {
      liveStatus = LiveStatus.generating;
    } else if (json['liveStatus'] == 3) {
      liveStatus = LiveStatus.generated;
    }
    return CourseTableLiveItemModel(
      liveStatus: liveStatus,
      liveCover: json['liveCover'],
      liveTitle: json['liveTitle'],
      liveTime: json['liveTime'],
      liveUrl: json['liveUrl'],
      roomId: json['roomId'],
      videoId: json['videoId'],
      businessTitle: json['businessTitle'],
      businessType: json['businessType'],
      appId: json['appId'],
      tags: json['tags'],
    );
  }
}
