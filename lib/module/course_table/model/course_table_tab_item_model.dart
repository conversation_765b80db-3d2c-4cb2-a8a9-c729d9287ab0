// 课程tab类型
enum CourseTabEnum {
  // 课程列表
  course,
  // H5页面
  webView,
}



class CourseTableTabItemModel {
  // 课程tab类型
  final CourseTabEnum? tabEnum;
  // 课程tab标题
  final String? title;
  // 课程tab值, tabEnum=webView时，value为url
  final String? value;

  CourseTableTabItemModel({this.tabEnum, this.title, this.value});

  factory CourseTableTabItemModel.fromJson(Map<String, dynamic> json) {
    return CourseTableTabItemModel(
      tabEnum: json['tabType'] == 1 ? CourseTabEnum.course : CourseTabEnum.webView,
      title: json['title'],
      value: json['value'],
    );
  }
}
