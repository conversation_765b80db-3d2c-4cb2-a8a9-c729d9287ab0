import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/enum/cq_class_package_enum.dart';

/// 包含直播列表+课包列表model
class CourseTableMyDataModel {
  List<CourseTableLiveItemModel>? liveList;
  List<CourseTableClassPackageModel>? packageList;

  CourseTableMyDataModel({this.liveList, this.packageList});

  CourseTableMyDataModel copyWith({
    List<CourseTableLiveItemModel>? liveList,
    List<CourseTableClassPackageModel>? packageList,
  }) {
    return CourseTableMyDataModel(
      liveList: liveList ?? this.liveList,
      packageList: packageList ?? this.packageList,
    );
  }
}

/// 课包列表itemModel
class CourseTableClassPackageModel {
  String? title;
  String? bgCover;
  String? name;
  int? packageType;
  String? liveUrl;

  CourseTableClassPackageModel({this.title, this.bgCover, this.name, this.packageType, this.liveUrl});

  factory CourseTableClassPackageModel.fromJson(Map<String, dynamic> json) {
    return CourseTableClassPackageModel(
      title: json['title'],
      bgCover: json['bgCover'],
      name: json['name'],
      packageType: json['packageType'],
      liveUrl: json['liveUrl'],
    );
  }
}
