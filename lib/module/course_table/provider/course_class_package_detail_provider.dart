import 'package:changqing_health_app/enum/cq_class_package_enum.dart';
import 'package:changqing_health_app/module/course_table/api/course_table_apis.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_my_data_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'course_class_package_detail_provider.g.dart';

/// 陪伴营系列列表
@Riverpod(keepAlive: true)
class CourseClassPackageType extends _$CourseClassPackageType {
  @override
  FutureOr<List<CourseClassPackageDetailTypeModel>?> build() async {
    return await CourseTableApi.getCourseSeriesList();
  }
}

// 课包详情----课表列表
@riverpod
Future<List<CourseTableLiveItemModel>> getClassDetailListData(
  Ref ref, {
  int page = 1,
  int pageSize = 10,
  required int packageType,
  String? seriesType,
}) async {
  // 新增逻辑，列表中，正在直播的数据要展示在最顶部
  // 所以当page=1时，要请求直播中的数据+未直播的数据
  if (page != 1) {
    return await _getClassDetailNotLiveListData(ref, packageType: packageType, pageSize: pageSize, page: page, seriesType: seriesType);
  }

  final results = await Future.wait([
    _getClassDetailLiveListData(ref, packageType: packageType, pageSize: pageSize, page: page, seriesType: seriesType),
    _getClassDetailNotLiveListData(ref, packageType: packageType, pageSize: pageSize, page: page, seriesType: seriesType),
  ]);

  final List<CourseTableLiveItemModel>? liveList = results[0] as List<CourseTableLiveItemModel>?;
  final List<CourseTableLiveItemModel>? notLiveList = results[1] as List<CourseTableLiveItemModel>?;
  List<CourseTableLiveItemModel> value = [];
  if (liveList != null) {
    value.addAll(liveList);
  }
  if (notLiveList != null) {
    value.addAll(notLiveList);
  }
  return value;
}

/// 获取正在直播中的数据
Future<List<CourseTableLiveItemModel>> _getClassDetailLiveListData(
  Ref ref, {
  int page = 1,
  int pageSize = 10,
  required int packageType,
  String? seriesType,
}) async {
  final params = {'page': page, 'page_size': pageSize, "seriesType": seriesType, "packageType": packageType, "isLive": '1'};
  // 新增逻辑，列表中，正在直播的数据要展示在最顶部
  // 所以当page=1时，要请求直播中的数据+未直播的数据

  final list = await CourseTableApi.getCourseRecords(params);
  return list ?? [];
}

/// 获取不在直播中的数据
Future<List<CourseTableLiveItemModel>> _getClassDetailNotLiveListData(
  Ref ref, {
  int page = 1,
  int pageSize = 10,
  required int packageType,
  String? seriesType,
}) async {
  final params = {'page': page, 'page_size': pageSize, "seriesType": seriesType, "packageType": packageType, "isLive": '0'};
  final list = await CourseTableApi.getCourseRecords(params);
  return list ?? [];
}
