import 'package:changqing_health_app/module/course_table/api/course_table_apis.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_my_data_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'course_table_my_provider.g.dart';


bool courseTableLiveIsLoad = false;

@riverpod
class CourseTableMy extends _$CourseTableMy {
  @override
  FutureOr<CourseTableMyDataModel?> build() async {
    return null;
  }

  /// 同时请求 直播 和 课包数据
  Future<CourseTableMyDataModel?> getCourseTableMyDataList() async {
    try {
      final results = await Future.wait([
        getCourseTableLiveList(),
        getCourseTableClassList(),
      ]);
      courseTableLiveIsLoad = true;
      final List<CourseTableLiveItemModel>? liveList = results[0] as List<CourseTableLiveItemModel>?;
      final List<CourseTableClassPackageModel>? classList = results[1] as List<CourseTableClassPackageModel>?;
      CourseTableMyDataModel model = CourseTableMyDataModel();
      model.liveList = liveList;
      model.packageList = classList;
      state = AsyncValue.data(model);
      return model;
    } catch (_) {
      return null;
    }
  }


  /// 今日直播数据
  Future<List<CourseTableLiveItemModel>?> getCourseTableLiveList() async {
    try {
      return CourseTableApi.refreshCourseTableLiveList();
    } catch (_) {}
    return null;
  }

  /// 课包列表
  Future<List<CourseTableClassPackageModel>?> getCourseTableClassList() async {
    try {
      return CourseTableApi.refreshCourseTableClassList();
    } catch (_) {}
    return null;
  }

  bool isEmptyData() {
    if (state.value == null) {
      return true;
    }
    bool empty = true;
    if (state.value!.liveList?.isNotEmpty ?? false) {
      empty = false;
    }
    if (state.value!.packageList?.isNotEmpty ?? false) {
      empty = false;
    }
    return empty;
  }
}
