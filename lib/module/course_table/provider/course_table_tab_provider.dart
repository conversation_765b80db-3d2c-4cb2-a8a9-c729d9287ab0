import 'package:changqing_health_app/module/course_table/api/course_table_apis.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_tab_item_model.dart';
import 'package:flutter/widgets.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'course_table_tab_provider.g.dart';


bool courseTableTabIsLoad = false;

@riverpod
class CourseTableTab extends _$CourseTableTab {
  @override
  FutureOr<List<CourseTableTabItemModel>> build() async {
    ref.onDispose((){
      debugPrint('CourseTableTab --- dispose');
    });
    courseTableTabIsLoad = true;
    return await refreshCourseTableTabList();
  }

  Future<List<CourseTableTabItemModel>> refreshCourseTableTabList() async {
    final res = await CourseTableApi.refreshCourseTableTabList();
    state = AsyncValue.data(res);
    return res;
  }
}
