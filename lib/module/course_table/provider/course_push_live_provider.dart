import 'package:changqing_health_app/enum/cq_live_enum.dart';
import 'package:changqing_health_app/module/course_table/api/course_table_apis.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/h5liveroom/util/h5_scheme_manager.dart';
import 'package:changqing_health_app/util/toast.dart';

class CoursePushLiveProvider {
  /// 跳转H5直播间
  static void pushLivePage(CourseTableLiveItemModel model) async {
    if (model.liveStatus == LiveStatus.notStarted) {
      Toast.show('当前直播未开始哦');
      return;
    }
    if (model.liveStatus == LiveStatus.generating) {
      Toast.show('回放生成中，请稍后刷新');
      return;
    }

    // 获取播放地址
    String? liveUrl;
    try {
      Toast.showLoading();
      final params = {'roomId': model.roomId, 'videoId': model.videoId, "appId": model.appId};
      liveUrl = await CourseTableApi.getLiveAddress(params);
    }catch(_){}
    Toast.closeLoading();

    if(liveUrl == null) {
      Toast.showMessage('获取直播间地址失败，请重试');
      return;
    }

    if (model.liveStatus == LiveStatus.live) {
      H5SchemeManager.openLiveUrlOnNewPage(liveUrl ?? '', title: model.liveTitle ?? model.businessTitle);
      return;
    }

    if (model.liveStatus == LiveStatus.generated) {
      H5SchemeManager.openLiveUrlOnNewPage(liveUrl ?? '', title: model.liveTitle ?? model.businessTitle);
      return;
    }
  }
}
