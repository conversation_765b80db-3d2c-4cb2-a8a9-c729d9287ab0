import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_my_data_model.dart';
import 'package:changqing_health_app/module/course_table/model/course_table_tab_item_model.dart';
import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:flutter/material.dart';

class CourseTableApi {
  CourseTableApi._();

  /// 学习tab数据
  static Future<List<CourseTableTabItemModel>> refreshCourseTableTabList() async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/tabs');
      final originData = response.data as List<dynamic>;
      final courseTableTabList = originData.map((e) => CourseTableTabItemModel.fromJson(e)).toList();
      return courseTableTabList;
    } catch (e) {
      print('Error during refreshCourseTableTabList: $e');
      return [CourseTableTabItemModel(tabEnum: CourseTabEnum.course, title: '我的课程')];
    }
  }

  /// 今日直播
  /// http://api-doc.weimiaocaishang.com/project/1376/interface/api/101398
  static Future<List<CourseTableLiveItemModel>?> refreshCourseTableLiveList() async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/today_live_list');
      final originData = response.data as List<dynamic>;
      final courseTableLiveList = originData.map((e) => CourseTableLiveItemModel.fromJson(e)).toList();
      return courseTableLiveList;
    } catch (e) {
      return null;
    }
  }

  /// 课包列表
  /// http://api-doc.weimiaocaishang.com/project/1376/interface/api/101395
  static Future<List<CourseTableClassPackageModel>?> refreshCourseTableClassList() async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/package_list');
      final originData = response.data as List<dynamic>;
      final courseTableClassPackageList = originData.map((e) => CourseTableClassPackageModel.fromJson(e)).toList();
      return courseTableClassPackageList;
    } catch (e) {
      return null;
    }
  }

  /// 陪伴营系列列表
  /// http://api-doc.weimiaocaishang.com/project/1376/interface/api/101404
  static Future<List<CourseClassPackageDetailTypeModel>?> getCourseSeriesList() async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/series_list');
      final originData = response.data as List<dynamic>;
      final typeList = originData.map((e) => CourseClassPackageDetailTypeModel.fromJson(e)).toList();
      return typeList;
    } catch (e) {
      return null;
    }
  }


  /// 根据课包列表获取课程列表
  /// http://api-doc.weimiaocaishang.com/project/1376/interface/api/101401
  static Future<List<CourseTableLiveItemModel>?> getCourseRecords(Map<String, dynamic> params) async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/records', param: params);
      final originData = response.data['list'] as List<dynamic>;
      final typeList = originData.map((e) => CourseTableLiveItemModel.fromJson(e)).toList();
      return typeList;
    } catch (e) {
      return null;
    }
  }

  /// 获取视频地址(直播/回放)
  /// http://api-doc.weimiaocaishang.com/project/1376/interface/api/101557
  static Future<String?> getLiveAddress(Map<String, dynamic> params) async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/get_live_address', param: params);
      final address = response.data['liveUrl'];
      return address;
    } catch (e) {
      return null;
    }
  }

}
