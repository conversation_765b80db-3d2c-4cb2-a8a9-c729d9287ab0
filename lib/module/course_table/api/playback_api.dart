import 'package:changqing_health_app/module/course_table/model/course_table_live_model.dart';
import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:flutter/cupertino.dart';

class PlaybackApi {
  PlaybackApi._();

  /// 获取回放列表
  static Future<List<CourseTableLiveItemModel>> getPlaybackData(Map<String, dynamic> params) async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/course/replay_list', param: params);
      if (response == null) return [];
      final originData = response.data as List<dynamic>;
      final playbackData = originData.map((e) => CourseTableLiveItemModel.fromJson(e)).toList();
      return playbackData;
    } catch (e) {
      debugPrint('Error during getPlaybackData: $e');
      return [];
    }
  }
}
