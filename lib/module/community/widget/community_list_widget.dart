import 'package:changqing_health_app/module/community/enum/community_enum.dart';
import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:changqing_health_app/module/community/provider/community_provider.dart';
import 'package:changqing_health_app/module/community/widget/community_box_widget.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/widget/app_empty_refresh_tips_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CommunityListWidget extends ConsumerStatefulWidget {
  final int labelId;

  const CommunityListWidget({super.key, required this.labelId});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<CommunityListWidget> {
  late final List<CommunityItem> _data = ref.read(communityAllDataProvider.notifier).getDataByLabelId(widget.labelId);

  @override
  Widget build(BuildContext context) => _buildContent(_data);

  Widget _buildContent(List<CommunityItem> data) {
    if (data.isEmpty) {
      return AppEmptyRefreshTipsWidget(
        onRefresh: () {
          ref.refresh(communityAllDataProvider);
        },
      );
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        // 是否是审核状态
        final isReview = ref.read(appVersionProviderProvider.notifier).isReview;
        return SizedBox(
          height: constraints.maxHeight,
          child: SingleChildScrollView(
            child: Column(
              children: [
                CommunityBoxWidget(
                  communityList: data.where((e) => e.column?.id == ColumnId.thanks.id).toList(),
                  icon: ColumnId.thanks.icon,
                ),
                CommunityBoxWidget(
                  communityList: data.where((e) => e.column?.id == ColumnId.talentShare.id).toList(),
                  icon: ColumnId.talentShare.icon,
                ),
                if (!isReview)
                  CommunityBoxWidget(
                    communityList: data.where((e) => e.column?.id == ColumnId.customerVoice.id).toList(),
                    icon: ColumnId.customerVoice.icon,
                  ),
                CommunityBoxWidget(
                  communityList: data.where((e) => e.column?.id == ColumnId.customerComment.id).toList(),
                  icon: ColumnId.customerComment.icon,
                ),
                if (!isReview)
                  CommunityBoxWidget(
                    communityList: data.where((e) => e.column?.id == ColumnId.nutritionistNote.id).toList(),
                    icon: ColumnId.nutritionistNote.icon,
                  ),
                if (!isReview)
                  CommunityBoxWidget(
                    communityList: data.where((e) => e.column?.id == ColumnId.encyclopedia.id).toList(),
                    icon: ColumnId.encyclopedia.icon,
                  ),
                CommunityBoxWidget(
                  communityList: data.where((e) => e.column?.id == ColumnId.supermarket.id).toList(),
                  icon: ColumnId.supermarket.icon,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
