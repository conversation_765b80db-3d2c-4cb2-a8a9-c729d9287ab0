
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/extension/obj_ext.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/community/enum/community_enum.dart';
import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:changqing_health_app/module/community/provider/community_provider.dart';
import 'package:changqing_health_app/module/community/route/community_route.dart';
import 'package:changqing_health_app/module/video/route/video_route.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class CommunityItemWidget extends ConsumerStatefulWidget {
  final CommunityItem communityItem;
  final VoidCallback? buryingPointCallBack;

  /// 是否是超市视频
  final bool isMarket;

  const CommunityItemWidget({super.key, required this.communityItem, this.isMarket = false, this.buryingPointCallBack});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<CommunityItemWidget> {
  CommunityItem get _item => widget.communityItem;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.isMarket ? 343.w : 300.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCover(),
          12.gap,
          _buildTitle(),
          6.gap,
          _buildSubTitle(),
        ],
      ),
    ).onTap(_onItemTap);
  }

  Widget _buildTitle() {
    return Text(
      _item.title ?? '',
      maxLines: widget.isMarket ? 2 : 1,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(
        fontSize: 26.sp,
        fontWeight: widget.isMarket ? FontWeight.w500 : FontWeight.normal,
      ),
    );
  }

  Widget _buildSubTitle() {
    if (widget.isMarket) return const SizedBox.shrink();
    return Text(
      _item.subTitle ?? '',
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
      style: TextStyle(fontSize: 24.sp, color: Colors.grey),
    );
  }

  Widget _buildCover() {
    return Container(
      width: widget.isMarket ? 343.w : 300.w,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.isMarket ? 16.w : 20.w),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          CQCachedNetworkImage(
            imageUrl: _item.cover ?? '',
            width: widget.isMarket ? 343.w : 300.w,
            height: 200.w,
            fit: BoxFit.cover,
          ),
          if (_item.contentType == ContentType.video.name)
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.4),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.play_arrow_rounded,
                size: 40.w,
                color: Colors.white.withValues(alpha: 0.8),
              ),
            )
        ],
      ),
    );
  }

  Future<void> _onItemTap() async {
    try {
      widget.buryingPointCallBack?.call();
    } catch (_) {}
    if (_item.contentType == ContentType.video.name) {
      final communityDetail = await ref.read(getCommunityDetailProvider(_item.id).future);
      if (mounted && !communityDetail.content.isBlank) {
        VideoRoute(
          videoUrl: communityDetail.content!,
          categoryName: _item.column?.name ?? '',
          title: _item.title ?? '',
          subTitle: _item.subTitle ?? '',
          videoId: _item.id.toString(),
        ).push(context);
      }
    } else if (_item.contentType == ContentType.richtext.name) {
      launchUrl(
        Uri.parse("https://mobile.cqslim.com/community-detail/${_item.id}?pageFrom=app"),
      );
      // CommunityArticleRoute(id: _item.id).push(context);
    }
  }
}
