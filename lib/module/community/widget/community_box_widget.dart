import 'dart:convert';

import 'package:changqing_health_app/constant/color_constant.dart';
import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/community/enum/community_enum.dart';
import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:changqing_health_app/module/community/route/community_route.dart';
import 'package:changqing_health_app/module/community/widget/community_item_widget.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 感谢
class CommunityBoxWidget extends ConsumerStatefulWidget {
  final List<CommunityItem> communityList;
  final String icon;

  const CommunityBoxWidget({super.key, required this.communityList, required this.icon});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<CommunityBoxWidget> {
  List<CommunityItem> get _dataList => widget.communityList;

  @override
  Widget build(BuildContext context) {
    if (_dataList.isEmpty) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: EdgeInsets.only(bottom: 16.w),
      child: Container(
        height: 450.w,
        width: context.sw - 32.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 32.w, top: 24.w, right: 12.w),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildIcon(),
                  12.gap,
                  Text(
                    _dataList.first.column?.name ?? '',
                    style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.bold),
                  ),
                  Spacer(),
                  if (_dataList.first.column?.id == ColumnId.supermarket.id)
                    GestureDetector(
                      onTap: () {
                        MarketVideoRoute().push(context);
                        AppBuryingPointUtil.click(modulePart: '178_cqjk_app_home_00010', moduleOri: '首页-点击【查看全部】');
                      },
                      child: Row(
                        children: [
                          Text('查看全部', style: TextStyle(fontSize: 26.sp, color: cqLightGreenColor)),
                          6.gap,
                          Icon(Icons.arrow_forward_ios_rounded, size: 28.w, color: cqLightGreenColor),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            12.gap,
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemCount: _dataList.length,
                itemBuilder: (context, index) {
                  final rightPadding = index == _dataList.length - 1 ? 32.w : 0.w;
                  return Padding(
                    padding: EdgeInsets.only(left: 32.w, right: rightPadding),
                    child: CommunityItemWidget(communityItem: _dataList[index], buryingPointCallBack: () => _buryingPoint(_dataList[index])),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIcon() {
    if (widget.icon.startsWith("http")) {
      return CQCachedNetworkImage(imageUrl: widget.icon, width: 44.w, height: 44.w);
    } else if (widget.icon.startsWith("data")) {
      return Image.memory(base64Decode(widget.icon.split(',').last), width: 44.w, height: 44.w);
    }
    return Image.asset(widget.icon, width: 44.w, height: 44.w);
  }

  void _buryingPoint(CommunityItem item) {
    String modulePart = '';
    String moduleOri = '';
    int? listId = _dataList.first.column?.id;
    if(ColumnId.thanks.id == listId) {
      modulePart = '178_cqjk_app_home_00004';
      moduleOri = '首页-感谢2024内容点击';
    }
    if(ColumnId.talentShare.id == listId) {
      modulePart = '178_cqjk_app_home_00005';
      moduleOri = '首页-达人分享内容点击';
    }
    if(ColumnId.customerVoice.id == listId) {
      modulePart = '178_cqjk_app_home_00006';
      moduleOri = '首页-客户心声内容点击';
    }
    if(ColumnId.nutritionistNote.id == listId) {
      modulePart = '178_cqjk_app_home_00007';
      moduleOri = '首页-营养师笔记内容点击';
    }
    if(ColumnId.encyclopedia.id == listId) {
      modulePart = '178_cqjk_app_home_00008	';
      moduleOri = '首页-营养轻百科内容点击';
    }
    if(ColumnId.supermarket.id == listId) {
      modulePart = '178_cqjk_app_home_00009';
      moduleOri = '首页-营养超市学内容点击';
    }
    AppBuryingPointUtil.click(modulePart: modulePart, moduleOri: moduleOri, alternate: {"videoId" : item.id});
  }
}
