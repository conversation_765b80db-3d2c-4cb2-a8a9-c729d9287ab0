import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:changqing_health_app/service/http/api_client.dart';

class CommunityApi {
  CommunityApi._();

  /// 获取专栏列表
  /// [params] 请求参数
  /// [page] 页码
  /// [page_size] 每页条数
  /// [label_id] 标签id
  static Future<List<CommunityItem>> getCommunityData(Map<String, dynamic> params) async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/community/list', param: params);
      final communityData = CommunityData.fromJson(response.data);
      return communityData.list;
    } catch (e) {
      print('Error during getCommunityData: $e');
      return [];
    }
  }

  /// 获取社区详情
  /// [id] id
  static Future<CommunityDetail> getCommunityDetail(int id) async {
    try {
      final response = await ApiClient.get('/api/cqapp/app/community/detail', param: {"id" : id});
      return CommunityDetail.fromJson(response.data);
    } catch (e) {
      throw Exception('Error during getCommunityDetail: $e');
    }
  }
}
