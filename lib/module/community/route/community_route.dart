import 'package:changqing_health_app/module/community/page/community_page.dart';
import 'package:changqing_health_app/module/community/page/community_web_page.dart';
import 'package:changqing_health_app/module/community/page/market_video_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

part 'community_route.g.dart';

/// 社区路由
class CommunityBranchSellData extends StatefulShellBranchData {
  const CommunityBranchSellData();
}

class CommunityRoute extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const CommunityPage();
  }
}

@TypedGoRoute<CommunityArticleRoute>(path: '/community/article')
class CommunityArticleRoute extends GoRouteData {
  final int id;

  const CommunityArticleRoute({required this.id});

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return CupertinoPage(child: CommunityWebPage(id: id));
  }
}

@TypedGoRoute<MarketVideoRoute>(path: '/community/market_video')
class MarketVideoRoute extends GoRouteData {
  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage(child: MarketVideoPage());
  }
}
