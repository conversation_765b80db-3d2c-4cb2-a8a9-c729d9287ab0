import 'package:changqing_health_app/module/community/api/community_api.dart';
import 'package:changqing_health_app/module/community/enum/community_enum.dart';
import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'community_provider.g.dart';

/// 标签列表
final labelListProvider = Provider<List<LabelItem>>((ref) {
  return [
    LabelItem(id: -1, name: '全部'),
    LabelItem(id: 1, name: '三高专区'),
    LabelItem(id: 2, name: '糖尿病专区'),
    LabelItem(id: 3, name: '减重专区'),
    LabelItem(id: 4, name: '心血管专区'),
    LabelItem(id: 5, name: '肝病专区'),
    LabelItem(id: 6, name: '蜕变达人秀'),
  ];
});

/// 获取社区详情
/// [id] id
@riverpod
Future<CommunityDetail> getCommunityDetail(Ref ref, int id) async {
  final communityDetail = await CommunityApi.getCommunityDetail(id);
  return communityDetail;
}

/// 获取全部专栏数据列表
@riverpod
class CommunityAllData extends _$CommunityAllData {
  @override
  Future<List<CommunityItem>> build() async {
    try {
      state = const AsyncLoading();
      final dataList = await CommunityApi.getCommunityData({'page': 1, 'page_size': 1000, 'label_id': null});
      return dataList;
    } catch (e) {
      return [];
    }
  }

  /// 根据标签id获取数据
  List<CommunityItem> getDataByLabelId(int labelId) {
    final currentState = state;
    if (currentState.hasValue) {
      if (labelId == -1) {
        return currentState.value!;
      }
      final currentList = currentState.value!;
      return currentList.where((e) => e.labels != null && e.labels!.any((element) => element.id == labelId)).toList();
    }
    return [];
  }

  /// 获取超市专栏数据
  List<CommunityItem> getSuperMarketList() {
    final currentState = state;
    if (currentState.hasValue) {
      final currentList = currentState.value!;
      return currentList.where((e) => e.column?.id == ColumnId.supermarket.id).toList();
    }
    return [];
  }
}
