import 'package:changqing_health_app/app/init.dart';
import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:changqing_health_app/module/community/provider/community_provider.dart';
import 'package:changqing_health_app/module/community/widget/community_item_widget.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/widget/route_aware_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

/// 超市视频
class MarketVideoPage extends ConsumerStatefulWidget {
  const MarketVideoPage({super.key});

  @override
  ConsumerState<MarketVideoPage> createState() => _MarketVideoPageState();
}

class _MarketVideoPageState extends ConsumerState<MarketVideoPage> with TickerProviderStateMixin {
  late TabController _tabCtrl;
  late final List<CommunityItem> _supermarketList;

  final List<ClassifiesItem> _tabList = [];

  @override
  void initState() {
    super.initState();
    _supermarketList = ref.read(communityAllDataProvider.notifier).getSuperMarketList();
    // 获取所有分类
    if (_supermarketList.isNotEmpty) {
      final set = <ClassifiesItem>{};
      for (var item in _supermarketList) {
        if (item.classifies != null) {
          set.addAll(item.classifies!);
        }
      }
      _tabList.add(ClassifiesItem(id: -1, name: '全部'));
      _tabList.addAll(set.toList());
      _tabCtrl = TabController(length: _tabList.length, vsync: this);
      _tabCtrl.addListener(_tabListener);
    }
    _buryingPoint();
  }

  void _tabListener() {
    if (_tabCtrl.indexIsChanging) return;
    AppBuryingPointUtil.click(modulePart: '178_cqjk_app_home_00012', moduleOri: '首页-营养超市学页面-顶部tab', alternate: {"id": _tabList[_tabCtrl.index].id, "name": _tabList[_tabCtrl.index].name});
  }

  void _buryingPoint(){
    AppBuryingPointUtil.show(modulePart: '178_cqjk_app_home_00011', moduleOri: '首页-营养超市学页面展示');
  }

  @override
  void dispose() {
    _tabCtrl.removeListener(_tabListener);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: Container(
          color: Colors.transparent,
          width: context.sw,
          height: kToolbarHeight,
          alignment: Alignment.centerLeft,
        ),
      ),
      body: Column(
        children: [
          _buildAppBar(),
          _buildTabBar(),
          _buildTabView(),
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return Container(
      width: context.sw,
      decoration: BoxDecoration(
        color: Colors.transparent,
        border: Border(
          bottom: BorderSide(color: const Color(0xff999999), width: 0.5.w),
        ),
      ),
      height: kToolbarHeight / 1.3,
      alignment: Alignment.centerLeft,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(onPressed: () => context.pop(), icon: Icon(Icons.arrow_back_rounded, size: 36.w)),
          Text('超市营养学', style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold)),
          IconButton(onPressed: null, icon: Icon(Icons.arrow_back_ios_new_rounded, size: 36.w, color: Colors.transparent)),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      // height: 60.w,
      width: context.sw,
      color: Colors.transparent,
      child: TabBar(
        padding: EdgeInsets.only(right: 24.w),
        dividerColor: const Color(0xffEEEEEE),
        dividerHeight: 0.5.w,
        tabAlignment: TabAlignment.start,
        isScrollable: true,
        controller: _tabCtrl,
        labelColor: const Color(0xff25b569),
        unselectedLabelColor: const Color(0xff666666),
        unselectedLabelStyle: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.w400),
        labelStyle: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold),
        splashFactory: NoSplash.splashFactory,
        indicatorColor: Colors.transparent,
        labelPadding: EdgeInsets.only(left: 24.w, right: 0),
        indicatorSize: TabBarIndicatorSize.label,
        tabs: [for (int i = 0; i < _tabList.length; i++) Tab(text: _tabList[i].name)],
      ),
    );
  }

  Widget _buildTabView() {
    return Expanded(
      child: TabBarView(
        controller: _tabCtrl,
        children: _tabList.map((e) => _SuperMarketListWidget(classifyId: e.id, supermarketList: _supermarketList)).toList(),
      ),
    );
  }
}

/// 超市视频列表
class _SuperMarketListWidget extends ConsumerStatefulWidget {
  /// 分类id
  final int classifyId;

  /// 超市列表
  final List<CommunityItem> supermarketList;

  const _SuperMarketListWidget({required this.classifyId, required this.supermarketList});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SuperMarketListWidgetState();
}

class _SuperMarketListWidgetState extends ConsumerState<_SuperMarketListWidget> {
  late List<CommunityItem> _dataList;

  @override
  void initState() {
    super.initState();
    if (widget.classifyId == -1) {
      _dataList = widget.supermarketList;
    } else {
      _dataList = widget.supermarketList.where((e) => e.classifies?.any((element) => element.id == widget.classifyId) ?? false).toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.only(left: 24.w, right: 24.w),
        child: Wrap(
          spacing: 16.w,
          runSpacing: 32.w,
          children: _dataList.map((e) => CommunityItemWidget(communityItem: e, isMarket: true, buryingPointCallBack: ()=> _buryingPoint(e),)).toList(),
        ),
      ),
    );
  }

  void _buryingPoint(CommunityItem communityItem){
    AppBuryingPointUtil.click(modulePart: '178_cqjk_app_home_00013', moduleOri: '首页-营养超市学页面-点击视频', alternate: {"videoId" : communityItem.id});
  }
}
