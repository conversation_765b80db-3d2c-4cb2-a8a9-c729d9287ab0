import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/community/provider/community_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CommunityWebPage extends ConsumerStatefulWidget {
  final int id;

  const CommunityWebPage({required this.id, super.key});

  @override
  ConsumerState<CommunityWebPage> createState() => _CommunityWebPageState();
}

class _CommunityWebPageState extends ConsumerState<CommunityWebPage> {
  @override
  Widget build(BuildContext context) {
    final communityDetail = ref.watch(getCommunityDetailProvider(widget.id));
    return Scaffold(
      appBar: AppBar(title: _buildTitle(), backgroundColor: Theme.of(context).scaffoldBackgroundColor),
      body: communityDetail.when(
        data: (data) => SingleChildScrollView(
          child: Html(
            data: data.content,
            extensions: [
              TagExtension(
                tagsToExtend: {'img'},
                builder: (context) {
                  final src = context.element!.attributes['src'];
                  if (src == null) {
                    return const SizedBox.shrink();
                  }
                  return CachedNetworkImage(
                    imageUrl: src,
                    fit: BoxFit.cover,
                  );
                },
              ),
            ],
          ),
        ),
        error: (error, stack) => Text(error.toString()),
        loading: () => const Center(child: CupertinoActivityIndicator()),
      ),
    );
  }

  Widget _buildTitle() {
    return Consumer(builder: (context, ref, _) {
      final request = ref.watch(getCommunityDetailProvider(widget.id));
      return request.when(
        data: (data) => Text(data.title ?? ''),
        error: (error, stack) => const SizedBox.shrink(),
        loading: () => const SizedBox.shrink(),
      );
    });
  }
}
