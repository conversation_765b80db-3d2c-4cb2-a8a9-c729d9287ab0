import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/community/model/community_model.dart';
import 'package:changqing_health_app/module/community/provider/community_provider.dart';
import 'package:changqing_health_app/module/community/widget/community_list_widget.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/widget/cq_loading_widget.dart';
import 'package:changqing_health_app/widget/keep_alive_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 社区页面
class CommunityPage extends ConsumerStatefulWidget {
  const CommunityPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<CommunityPage> with TickerProviderStateMixin {
  late TabController _tabCtrl;
  late List<LabelItem> _labelList;

  @override
  void initState() {
    super.initState();
    _labelList = ref.read(labelListProvider);
    _tabCtrl = TabController(length: _labelList.length, vsync: this);
    _tabCtrl.addListener(_tabListener);
    try {
      AppBuryingPointUtil.show(modulePart: '178_cqjk_app_home_00002', moduleOri: '访问学习页面【我的课程】');
    } catch (_) {}
  }

  void _tabListener() {
    if (_tabCtrl.indexIsChanging) return;
    LabelItem item = _labelList[_tabCtrl.index];
    AppBuryingPointUtil.click(modulePart: '178_cqjk_app_home_00003', moduleOri: '首页-tab点击', alternate: {"tabName": item.name, "tabId": item.id});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(0),
        child: Container(
          color: Colors.transparent,
          width: context.sw,
          height: kToolbarHeight,
          alignment: Alignment.centerLeft,
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTabBar(),
          _buildTabView(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      // height: 88.w,
      width: context.sw,
      color: Colors.transparent,
      child: TabBar(
        padding: EdgeInsets.only(right: 40.w),
        dividerColor: const Color(0xffEEEEEE),
        dividerHeight: 0.5.w,
        tabAlignment: TabAlignment.start,
        isScrollable: true,
        controller: _tabCtrl,
        labelColor: const Color(0xff25b569),
        unselectedLabelColor: const Color(0xff666666),
        unselectedLabelStyle: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.w400),
        labelStyle: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
        splashFactory: NoSplash.splashFactory,
        indicatorColor: Colors.transparent,
        labelPadding: EdgeInsets.only(left: 40.w, right: 0),
        indicatorSize: TabBarIndicatorSize.label,
        tabs: [for (int i = 0; i < _labelList.length; i++) Tab(text: _labelList[i].name)],
      ),
    );
  }

  Widget _buildTabView() {
    final request = ref.watch(communityAllDataProvider);
    return Expanded(
      child: request.when(
        data: (data) => TabBarView(
          // physics: NeverScrollableScrollPhysics(),
          controller: _tabCtrl,
          children: _labelList.map((e) => Center(child: KeepAliveWidget(child: CommunityListWidget(labelId: e.id)))).toList(),
        ),
        error: (error, stack) => Text(error.toString()),
        loading: () => const Center(child: CQLoadingWidget()),
      ),
    );
  }
}
