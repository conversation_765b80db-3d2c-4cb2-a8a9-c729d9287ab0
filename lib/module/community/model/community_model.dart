import 'package:freezed_annotation/freezed_annotation.dart';

part 'community_model.freezed.dart';
part 'community_model.g.dart';

@freezed
class CommunityData with _$CommunityData {
  const factory CommunityData({
    required List<CommunityItem> list,
    required int total,
  }) = _CommunityData;

  factory CommunityData.fromJson(Map<String, dynamic> json) => _$CommunityDataFromJson(json);
}

@freezed
class CommunityItem with _$CommunityItem {
  const factory CommunityItem({
    required int id,
    String? uuid,
    String? title,
    @JsonKey(name: 'subtitle') String? subTitle,
    String? keywords,
    String? author,
    @JsonKey(name: 'culture_date') String? cultureDate,
    String? cover,
    @JsonKey(name: 'content_type') String? contentType,
    @J<PERSON><PERSON>ey(name: 'is_show') int? isShow,
    int? sort,
    @J<PERSON><PERSON><PERSON>(name: 'update_time') String? updateTime,
    List<LabelItem>? labels,
    ColumnItem? column,
    List<ClassifiesItem>? classifies,
  }) = _CommunityItem;

  factory CommunityItem.fromJson(Map<String, dynamic> json) => _$CommunityItemFromJson(json);
}

/// 标签
@freezed
class LabelItem with _$LabelItem {
  const factory LabelItem({
    /// 标签id
    required int id,

    /// 标签名称
    String? name,
  }) = _LabelItem;

  factory LabelItem.fromJson(Map<String, dynamic> json) => _$LabelItemFromJson(json);
}

/// 专栏
@freezed
class ColumnItem with _$ColumnItem {
  const factory ColumnItem({
    /// 专栏id
    required int id,

    /// 专栏名称
    String? name,
  }) = _ColumnItem;

  factory ColumnItem.fromJson(Map<String, dynamic> json) => _$ColumnItemFromJson(json);
}

/// 分类
@freezed
class ClassifiesItem with _$ClassifiesItem {
  const factory ClassifiesItem({
    required int id,
    int? sort,
    String? name,
  }) = _ClassifiesItem;

  factory ClassifiesItem.fromJson(Map<String, dynamic> json) => _$ClassifiesItemFromJson(json);
}

// 社区详情
@freezed
class CommunityDetail with _$CommunityDetail {
  const factory CommunityDetail({
    required int id,
    String? uuid,
    String? title,
    @JsonKey(name: 'subtitle') String? subTitle,
    String? keywords,
    String? author,
    @JsonKey(name: 'culture_date') String? cultureDate,
    String? cover,
    @JsonKey(name: 'content_type') String? contentType,
    String? content,
    @JsonKey(name: 'video_name') String? videoName,
    @JsonKey(name: 'is_show') int? isShow,
    int? sort,
    @JsonKey(name: 'update_time') String? updateTime,
    String? deletedAt,
    List<LabelItem>? labels,
    ColumnItem? column,
  }) = _CommunityDetail;

  factory CommunityDetail.fromJson(Map<String, dynamic> json) => _$CommunityDetailFromJson(json);
}
