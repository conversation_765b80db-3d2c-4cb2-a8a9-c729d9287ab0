import 'dart:io';

import 'package:flutter/services.dart';

/// 将毫秒转换为 mm:ss 格式
String formatMsToMMSS(int milliseconds) {
  final duration = Duration(milliseconds: milliseconds);
  final minutes = duration.inMinutes;
  final seconds = duration.inSeconds % 60;
  return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
}

/// 退出全屏
void exitFullScreen() {
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
}

/// 进入全屏
void enterFullScreen() {
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
  if (Platform.isIOS) {
    SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeRight]);
  } else {
    SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft]);
  }
}
