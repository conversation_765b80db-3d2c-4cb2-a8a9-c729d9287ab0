import 'dart:async';

import 'package:changqing_health_app/app/init.dart';
import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/video/handler/video_seek_gesture_handler.dart';
import 'package:changqing_health_app/module/video/model/video_control_model.dart';
import 'package:changqing_health_app/module/video/util/video_util.dart';
import 'package:changqing_health_app/module/video/widget/bottom_progress_and_action_widget.dart';
import 'package:changqing_health_app/module/video/widget/full_screen_widget.dart';
import 'package:changqing_health_app/module/video/widget/loading_widget.dart';
import 'package:changqing_health_app/module/video/widget/play_widget.dart';
import 'package:changqing_health_app/module/video/widget/seek_position_widget.dart';
import 'package:changqing_health_app/module/video/widget/top_action_widget.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/widget/route_aware_widget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wm_ali_player/tx_video_player.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

class VideoPlayerPage extends StatefulWidget {
  // 视频url
  final String videoUrl;

  final String? videoId;

  // 分类名称
  final String? categoryName;

  // 标题
  final String? title;

  // 副标题
  final String? subTitle;

  const VideoPlayerPage({
    super.key,
    required this.videoUrl,
    this.categoryName,
    this.title,
    this.subTitle,
    this.videoId,
  });

  @override
  State<VideoPlayerPage> createState() => _VideoPlayerPageState();
}

class _VideoPlayerPageState extends State<VideoPlayerPage> with WidgetsBindingObserver {
  final TXVideoPlayerController _playerController = TXVideoPlayerController();
  late final ValueNotifier<ProgressInfo> _progressInfoNotifier = ValueNotifier(ProgressInfo.empty());

  String get videoUrl => widget.videoUrl;
  late final VideoSeekGestureHandler _videoSeekGestureHandler = VideoSeekGestureHandler(
    videoPlayerController: _playerController,
    progressInfoNotifier: _progressInfoNotifier,
    onDragEnd: () {
      if (!isPortrait) {
        _startHideTimer();
      }
    },
  );

  // 添加自动隐藏控制栏的定时器
  Timer? _hideControlsTimer;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _playerController.initialize().then((onValue) {
      _playerController.setLoop(true);
      _playerController.play(videoUrl).then((onValue) {
        setState(() {});
      });
    });
    // 监听进度信息变化，处理拖动状态
    _progressInfoNotifier.addListener(_handleProgressInfoChange);
    _buryingPoint();
  }

  @override
  void dispose() {
    // 在页面销毁前先重置系统UI样式
    resetSystemUIStyle();
    _disposePlayer();
    WidgetsBinding.instance.removeObserver(this);
    // 取消定时器
    _cancelHideTimer();
    _progressInfoNotifier.removeListener(_handleProgressInfoChange);
    _buryingPoint();
    super.dispose();
  }

  void _buryingPoint() {
    AppBuryingPointUtil.heartbeat(
      modulePart: '178_cqjk_app_home_00014',
      moduleOri: '首页-感谢2024/达人分享/超市营养学的视频页面浏览时长',
      alternate: {
        "videoId": widget.videoId ?? '',
        "hashCode" : hashCode,
      },
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.paused || state == AppLifecycleState.inactive) {
      _playerController.pause();
      _showControls();
    }
  }

  // 处理进度信息变化
  void _handleProgressInfoChange() {
    // 如果正在拖动，取消定时器
    if (_progressInfoNotifier.value.isDragging) {
      _cancelHideTimer();
    }
  }

  // 启动自动隐藏定时器
  void _startHideTimer() {
    // 先取消已有定时器
    _cancelHideTimer();

    // 创建新定时器
    _hideControlsTimer = Timer(const Duration(seconds: 3), () {
      final isPause = _playerController.value.isPause ?? false;
      final showActionBar = _progressInfoNotifier.value.showActionBar;
      if (mounted && showActionBar && !isPortrait && !isPause) {
        _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
          showActionBar: false,
        );
      }
    });
  }

  // 切换控制栏显示状态
  void _toggleControlsVisibility() {
    final newShowActionBar = !_progressInfoNotifier.value.showActionBar;

    _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
      showActionBar: newShowActionBar,
    );

    if (newShowActionBar) {
      _startHideTimer();
    } else {
      _cancelHideTimer();
    }
  }

  // 取消自动隐藏定时器
  void _cancelHideTimer() {
    _hideControlsTimer?.cancel();
    _hideControlsTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: PreferredSize(
      //   preferredSize: Size(100, 0),
      //   child: AppBar(
      //     title: const Text('Video Player'),
      //   ),
      // ),
      body: RouteAwareWidget(
        didPush: () {
          SystemChrome.setSystemUIOverlayStyle(
            SystemUiOverlayStyle(
              statusBarColor: Colors.black, // 状态栏背景色
              statusBarIconBrightness: Brightness.light, // 状态栏图标颜色

              systemNavigationBarColor: Colors.black,
              systemNavigationBarIconBrightness: Brightness.light, // 图标亮色
            ),
          );
        },
        didPop: () => resetSystemUIStyle(),
        child: PopScope(
          // 返回键
          canPop: isPortrait,
          onPopInvokedWithResult: (didPop, result) {
            if (!isPortrait) {
              exitFullScreen();
            }
          },
          child: OrientationBuilder(
            // 方向改变
            builder: (context, orientation) {
              if (orientation == Orientation.portrait) {
                _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
                  showActionBar: true,
                );
              } else {
                _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
                  showActionBar: _playerController.value.isPause ?? false,
                );
              }
              return LayoutBuilder(
                builder: (context, constrints) {
                  return RawGestureDetector(
                    gestures: {
                      if (orientation == Orientation.landscape) HorizontalDragGestureRecognizer: _videoSeekGestureHandler.createHorizontalDragGestureRecognizer(constrints),
                      TapGestureRecognizer: _videoSeekGestureHandler.createTapGestureRecognizer(_handleBodyTap),
                    },
                    child: Container(
                      color: Colors.black,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // 视频区域
                              _buildVideoArea(),
                              // 全屏按钮
                              FullScreenWidget(
                                playerController: _playerController,
                                onFullScreenCallback: () {
                                  _hideControls();
                                },
                              ),
                            ],
                          ),
                          // 拖拽进度条时，显示当前拖拽位置的进度
                          _buildSeekPosition(),
                          // 底部操作栏
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: _buildBottomActionWidget(constrints),
                          ),
                          // 顶部操作栏
                          Positioned(
                            top: MediaQuery.of(context).padding.top,
                            child: TopActionWidget(
                              progressInfoNotifier: _progressInfoNotifier,
                              title: widget.title,
                              subTitle: widget.subTitle,
                            ),
                          ),
                          // 顶部操作栏
                          // Positioned(
                          //   top: 100,
                          //   left: 0,
                          //   child: TopActionWidget(progressInfoNotifier: _progressInfoNotifier),
                          // ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// 拖拽进度条时，显示当前拖拽位置的进度
  Widget _buildSeekPosition() {
    return Positioned(
      bottom: 120,
      child: SeekPositionWidget(
        progressInfoNotifier: _progressInfoNotifier,
      ),
    );
  }

  /// 视频区域
  Widget _buildVideoArea() {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        TXVideoPlayer(_playerController),
        PlayWidget(
          playerController: _playerController,
          onPlayCallback: () {
            if (!isPortrait) {
              _startHideTimer();
            }
          },
        ),
        LoadingWidget(playerController: _playerController),
      ],
    );
  }

  Widget _buildBottomActionWidget(BoxConstraints constrints) {
    final bottomActionWidget = BottomProgressAndActionWidget(
      playerController: _playerController,
      progressInfoNotifier: _progressInfoNotifier,
      categoryName: widget.categoryName,
      subTitle: widget.subTitle,
      onManualPauseCallback: (isPause) {
        if (isPause) {
          _cancelHideTimer();
        } else {
          _startHideTimer();
        }
      },
    );

    // if (context.landscape) {
    //   return RawGestureDetector(
    //     gestures: {
    //       HorizontalDragGestureRecognizer: _videoSeekGestureHandler.createHorizontalDragGestureRecognizer(constrints),
    //     },
    //     behavior: HitTestBehavior.opaque,
    //     child: bottomActionWidget,
    //   );
    // }

    return bottomActionWidget;
  }

  /// 1. 竖屏模式下，点击暂停或者播放
  /// 2. 横屏模式下，点击会出现控制栏，同时视频中间会出现一个暂停按钮
  ///  2.1 横屏模式下再次点击，控制栏消失，视频中间的暂停按钮消失
  void _handleBodyTap() {
    if (isPortrait) {
      if (_playerController.value.isPlaying) {
        _playerController.pause();
      } else {
        _playerController.resume();
      }
    } else {
      _toggleControlsVisibility();
    }
  }

  /// 隐藏控制栏
  void _hideControls() {
    _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
      showActionBar: false,
    );
  }

  /// 显示控制栏
  void _showControls() {
    _progressInfoNotifier.value = _progressInfoNotifier.value.copyWith(
      showActionBar: true,
    );
  }

  Future<void> _disposePlayer() async {
    if (_playerController.value.isPlaying) {
      await _playerController.pause();
    }
    await _playerController.dispose();
  }

  bool get isPortrait => MediaQuery.of(context).orientation == Orientation.portrait;
}
