import 'package:changqing_health_app/module/video/page/video_player_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
part 'video_route.g.dart';

@TypedGoRoute<VideoRoute>(path: '/video')
class VideoRoute extends GoRouteData {
  final String videoUrl;
  final String? videoId;
  final String? categoryName;
  final String? title;
  final String? subTitle;
  VideoRoute({required this.videoUrl, this.categoryName, this.title, this.subTitle, this.videoId});
  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return CupertinoPage(
      child: VideoPlayerPage(
        videoUrl: videoUrl,
        categoryName: categoryName,
        title: title,
        subTitle: subTitle,
        videoId: videoId,
      ),
    );
  }
}
