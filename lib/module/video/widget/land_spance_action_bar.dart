import 'package:changqing_health_app/module/video/model/video_control_model.dart';
import 'package:changqing_health_app/module/video/util/video_util.dart';
import 'package:flutter/material.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

/// 横屏时，底部操作栏
/// 播放/暂停； 静音/取消静音； 当前播放进度：视频总时长； 退出全屏
class LandSpaceActionBar extends StatefulWidget {
  final TXVideoPlayerController playerController;
  final ValueNotifier<ProgressInfo> progressInfoNotifier;
  // 手动暂停/恢复播放 回调
  final Function(bool isPause)? onManualPauseCallback;
  const LandSpaceActionBar({
    super.key,
    required this.playerController,
    required this.progressInfoNotifier,
    this.onManualPauseCallback,
  });

  @override
  State<LandSpaceActionBar> createState() => _LandSpaceActionBarState();
}

class _LandSpaceActionBarState extends State<LandSpaceActionBar> {
  TXVideoPlayerController get _playerController => widget.playerController;
  ValueNotifier<ProgressInfo> get _progressInfoNotifier => widget.progressInfoNotifier;
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _progressInfoNotifier,
      builder: (context, progressInfo, child) {
        if (!_playerController.value.isInitialized) return const SizedBox.shrink();
        if (!_playerController.value.isPrepared) return const SizedBox.shrink();
        return Row(
          children: [
            switch (_playerController.value.isPlaying) {
              true => IconButton(
                  onPressed: () {
                    _playerController.pause();
                    widget.onManualPauseCallback?.call(true);
                  },
                  icon: const Icon(Icons.pause, color: Colors.white),
                ),
              false => IconButton(
                  onPressed: () {
                    _playerController.resume();
                    widget.onManualPauseCallback?.call(false);
                  },
                  icon: const Icon(Icons.play_arrow, color: Colors.white),
                ),
            },
            switch (_playerController.value.isMute) {
              true => IconButton(
                  onPressed: () {
                    _playerController.setMute(false);
                  },
                  icon: const Icon(Icons.volume_off, color: Colors.white),
                ),
              false => IconButton(
                  onPressed: () {
                    _playerController.setMute(true);
                  },
                  icon: const Icon(Icons.volume_up, color: Colors.white),
                ),
            },
            Text(
              '${formatMsToMMSS(progressInfo.currentMs)} / ${formatMsToMMSS(progressInfo.totalMs)}',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () {
                exitFullScreen();
              },
              icon: const Icon(Icons.fullscreen_exit, color: Colors.white),
            ),
          ],
        );
      },
    );
  }
}
