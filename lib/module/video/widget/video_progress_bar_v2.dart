import 'package:changqing_health_app/module/video/handler/video_seek_gesture_handler.dart';
import 'package:changqing_health_app/module/video/model/video_control_model.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

class VideoProgressBarV2 extends StatefulWidget {
  final TXVideoPlayerController playerController;
  // 添加 ValueNotifier
  final ValueNotifier<ProgressInfo> progressInfoNotifier;
  const VideoProgressBarV2({
    super.key,
    required this.playerController,
    required this.progressInfoNotifier,
  });

  @override
  State<VideoProgressBarV2> createState() => _VideoProgressBarV2State();
}

class _VideoProgressBarV2State extends State<VideoProgressBarV2> {
  TXVideoPlayerController get _videoPlayerController => widget.playerController;
  late final VideoSeekGestureHandler _videoSeekGestureHandler = VideoSeekGestureHandler(
    videoPlayerController: _videoPlayer<PERSON>ontroller,
    progressInfoNotifier: widget.progressInfoNotifier,
  );
  // 进度条最小高度和最大高度
  double get minProgressBarHeight => 3;
  double get maxProgressBarHeight => 12;
  // 是否是第一次构建
  bool _isFirstBuild = true;
  @override
  void initState() {
    super.initState();
    _videoPlayerController.onPlayerStateChanged.listen((value) {
      if (!value.isInitialized || !value.isPrepared) return;
      if (widget.progressInfoNotifier.value.isDragging || widget.progressInfoNotifier.value.isSeekLocked) return;
      widget.progressInfoNotifier.value = widget.progressInfoNotifier.value.copyWith(
        currentMs: value.position,
        totalMs: value.duration,
        progressPercent: value.position / value.duration,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 添加约束值检查
        if (!constraints.hasBoundedWidth || constraints.maxWidth.isNaN || constraints.maxWidth <= 0) {
          return SizedBox(height: maxProgressBarHeight * 1.5); // 返回一个占位组件，避免渲染错误
        }
        return RawGestureDetector(
          gestures: {
            HorizontalDragGestureRecognizer: _videoSeekGestureHandler.createHorizontalDragGestureRecognizer(constraints),
          },
          child: ValueListenableBuilder(
            valueListenable: widget.progressInfoNotifier,
            builder: (context, value, child) {
              if (value.totalMs == 0) return SizedBox(height: maxProgressBarHeight * 1.5);
              return Container(
                height: maxProgressBarHeight * 1.5,
                width: constraints.maxWidth.isNaN ? 0 : constraints.maxWidth,
                alignment: Alignment(-1.0, 1),
                decoration: BoxDecoration(
                  color: Colors.transparent, // 进度条背景
                ),
                child: TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 150),
                  tween: Tween<double>(
                    // begin: value.isDragging ? minProgressBarHeight : maxProgressBarHeight,
                    // end: value.isDragging ? maxProgressBarHeight : minProgressBarHeight,
                    begin: _isFirstBuild ? minProgressBarHeight : (value.isDragging ? minProgressBarHeight : maxProgressBarHeight),
                    end: _isFirstBuild ? minProgressBarHeight : (value.isDragging ? maxProgressBarHeight : minProgressBarHeight),
                  ),
                  builder: (context, height, child) {
                    if (_isFirstBuild) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _isFirstBuild = false;
                      });
                    }
                    final double factor = 1.8;
                    return TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 150),
                      tween: Tween<double>(
                        // begin: value.isDragging ? minProgressBarHeight * factor : maxProgressBarHeight * 1.2,
                        // end: value.isDragging ? maxProgressBarHeight * 1.2 : minProgressBarHeight * factor,
                        begin: _isFirstBuild ? minProgressBarHeight * factor : (value.isDragging ? minProgressBarHeight * factor : maxProgressBarHeight * 1.2),
                        end: _isFirstBuild ? minProgressBarHeight * factor : (value.isDragging ? maxProgressBarHeight * 1.2 : minProgressBarHeight * factor),
                      ),
                      builder: (context, dotHeight, _) {
                        return Stack(
                          children: [
                            // 进度条背景
                            Container(
                              width: constraints.maxWidth.isFinite ? constraints.maxWidth : 0,
                              height: height, // 高度使用动画值
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(50),
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            // 进度条
                            FractionallySizedBox(
                              widthFactor: value.progressPercent, // 使用比例因子而不是具体宽度
                              child: Container(
                                // width: constraints.maxWidth.isFinite ? constraints.maxWidth * displayProgress : 0,
                                height: height, // 高度使用动画值
                                decoration: BoxDecoration(
                                  // color: const Color(0xff55af60),
                                  color: Colors.white.withAlpha(125),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30),
                                    bottomLeft: Radius.circular(30),
                                  ),
                                ),
                                alignment: Alignment.centerRight,
                                // 进度条拖拽点
                                child: Stack(
                                  alignment: Alignment.centerRight,
                                  clipBehavior: Clip.none,
                                  children: [
                                    Positioned(
                                      right: -minProgressBarHeight,
                                      // top: -minProgressBarHeight / 2,
                                      child: Container(
                                        width: minProgressBarHeight * factor,
                                        height: dotHeight,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.circular(minProgressBarHeight),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }
}
