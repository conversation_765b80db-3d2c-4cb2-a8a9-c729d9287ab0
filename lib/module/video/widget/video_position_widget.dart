import 'package:changqing_health_app/module/video/model/video_control_model.dart';
import 'package:changqing_health_app/module/video/util/video_util.dart';
import 'package:flutter/material.dart';

class VideoPositionWidget extends StatelessWidget {
  final ValueNotifier<DragTimeInfo?> playPositionNotifier;
  const VideoPositionWidget({super.key, required this.playPositionNotifier});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<DragTimeInfo?>(
      valueListenable: playPositionNotifier,
      builder: (context, dragTimeInfo, child) {
        if (dragTimeInfo == null) return const SizedBox.shrink();

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
          child: Text(
            '${formatMsToMMSS(dragTimeInfo.currentMs)} / ${formatMsToMMSS(dragTimeInfo.totalMs)}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        );
      },
    );
  }
}
