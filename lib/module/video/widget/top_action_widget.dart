import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/video/model/video_control_model.dart';
import 'package:changqing_health_app/module/video/util/video_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 顶部操作栏
class TopActionWidget extends StatefulWidget {
  final ValueNotifier<ProgressInfo> progressInfoNotifier;
  final String? title;
  final String? subTitle;
  const TopActionWidget({
    super.key,
    required this.progressInfoNotifier,
    this.title,
    this.subTitle,
  });

  @override
  State<TopActionWidget> createState() => _TopActionWidgetState();
}

class _TopActionWidgetState extends State<TopActionWidget> {
  ValueNotifier<ProgressInfo> get _progressInfoNotifier => widget.progressInfoNotifier;

  @override
  Widget build(BuildContext context) {
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    return ValueListenableBuilder(
      valueListenable: _progressInfoNotifier,
      builder: (context, value, _) {
        return AnimatedPositioned(
          duration: const Duration(milliseconds: 350),
          curve: Curves.easeInOut,
          left: 0,
          right: 0,
          top: value.showActionBar ? MediaQuery.of(context).padding.top : -100,
          child: Container(
            height: 40,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.5),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withValues(alpha: 0.5),
                  Colors.transparent,
                ],
              ),
            ),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: context.paddingLeft),
                  child: IconButton(
                    onPressed: () {
                      if (isPortrait) {
                        Navigator.pop(context);
                      } else {
                        exitFullScreen();
                      }
                    },
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                  ),
                ),
                Expanded(
                  child: Text(
                    isPortrait ? widget.title ?? '' : widget.subTitle ?? '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Color(0xffe6e6e6),
                      fontSize: isPortrait ? 30.sp : 16.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
