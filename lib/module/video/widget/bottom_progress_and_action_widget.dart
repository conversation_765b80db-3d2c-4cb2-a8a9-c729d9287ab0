import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/video/model/video_control_model.dart';
import 'package:changqing_health_app/module/video/widget/land_spance_action_bar.dart';
import 'package:changqing_health_app/module/video/widget/video_progress_bar_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';

/// 底部进度条和操作栏
class BottomProgressAndActionWidget extends StatefulWidget {
  final TXVideoPlayerController playerController;
  final ValueNotifier<ProgressInfo> progressInfoNotifier;
  // 手动暂停/恢复播放 回调
  final Function(bool isPause)? onManualPauseCallback;
  // 分类名称
  final String? categoryName;
  // 副标题
  final String? subTitle;
  const BottomProgressAndActionWidget({
    super.key,
    required this.playerController,
    required this.progressInfoNotifier,
    this.onManualPauseCallback,
    this.categoryName,
    this.subTitle,
  });

  @override
  State<BottomProgressAndActionWidget> createState() => _BottomProgressAndActionWidgetState();
}

class _BottomProgressAndActionWidgetState extends State<BottomProgressAndActionWidget> {
  TXVideoPlayerController get _playerController => widget.playerController;
  ValueNotifier<ProgressInfo> get _progressInfoNotifier => widget.progressInfoNotifier;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: _progressInfoNotifier,
        builder: (context, value, _) {
          // return  Container(
          //   padding: EdgeInsets.only(
          //     // bottom: 0.w,
          //     bottom: MediaQuery.of(context).padding.top,
          //     left: context.portrait ? 12.w : (context.paddingLeft == 0 ? 30.w : context.paddingLeft),
          //     right: context.portrait ? 12.w : (context.paddingRight == 0 ? 30.w : context.paddingRight),
          //   ),
          //   width: MediaQuery.of(context).size.width,
          //   decoration: BoxDecoration(
          //     color: Colors.black.withValues(alpha: 0.5),
          //     gradient: LinearGradient(
          //       begin: Alignment.bottomCenter,
          //       end: Alignment.topCenter,
          //       colors: [
          //         Colors.black.withValues(alpha: 0.5),
          //         Colors.transparent,
          //       ],
          //     ),
          //   ),
          //   child: Column(
          //     children: [
          //       _buildInfo(),
          //       VideoProgressBarV2(
          //         playerController: _playerController,
          //         progressInfoNotifier: _progressInfoNotifier,
          //       ),
          //       if (MediaQuery.of(context).orientation == Orientation.portrait) (context.paddingBottom * 1.5).gap,
          //       if (MediaQuery.of(context).orientation == Orientation.landscape)
          //         LandSpaceActionBar(
          //           playerController: _playerController,
          //           progressInfoNotifier: _progressInfoNotifier,
          //           onManualPauseCallback: widget.onManualPauseCallback,
          //         ),
          //     ],
          //   ),
          // );
          return AnimatedPositioned(
            duration: const Duration(milliseconds: 350),
            curve: Curves.easeInOut,
            left: 0,
            right: 0,
            bottom: value.showActionBar ? 0 : -100,
            child: Container(
              padding: EdgeInsets.only(
                // bottom: 0.w,
                bottom: MediaQuery.of(context).padding.top,
                left: context.portrait ? 12.w : (context.paddingLeft == 0 ? 30.w : context.paddingLeft),
                right: context.portrait ? 12.w : (context.paddingRight == 0 ? 30.w : context.paddingRight),
              ),
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withValues(alpha: 0.5),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Column(
                children: [
                  _buildInfo(),
                  VideoProgressBarV2(
                    playerController: _playerController,
                    progressInfoNotifier: _progressInfoNotifier,
                  ),
                  if (MediaQuery.of(context).orientation == Orientation.portrait) (context.paddingBottom * 1.5).gap,
                  if (MediaQuery.of(context).orientation == Orientation.landscape)
                    LandSpaceActionBar(
                      playerController: _playerController,
                      progressInfoNotifier: _progressInfoNotifier,
                      onManualPauseCallback: widget.onManualPauseCallback,
                    ),
                ],
              ),
            ),
          );
        });
  }

  /// 构建底部信息
  Widget _buildInfo() {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.categoryName ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Color(0xffe6e6e6),
              fontSize: 32.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          4.gap,
          Text(
            widget.subTitle ?? '',
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 27.sp,
            ),
          ),
          12.gap,
        ],
      ),
    ).visibleInPortrait();
  }
}
