import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wm_ali_player/tx_video_player_controller.dart';
import 'package:wm_ali_player/tx_video_player_value.dart';

/// 视频加载动画
class LoadingWidget extends ConsumerStatefulWidget {
  final TXVideoPlayerController playerController;
  const LoadingWidget({super.key, required this.playerController});

  @override
  ConsumerState<LoadingWidget> createState() => _LoadingWidgetState();
}

class _LoadingWidgetState extends ConsumerState<LoadingWidget> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<TXVideoPlayerValue>(
      stream: widget.playerController.onPlayerStateChanged,
      builder: (context, snapshot) {
        if (snapshot.data == null) return _buildLoading();
        // 是否显示加载动画
        final showLoading = snapshot.data!.isLoading || !snapshot.data!.isPrepared || !snapshot.data!.isInitialized;
        // 加载中的文字
        final loadText = switch (snapshot.data!.isLoading) {
          true => '${snapshot.data!.netSpeed}kb/s',
          false => '加载中...',
        };
        return showLoading
            ? Container(
                padding: EdgeInsets.symmetric(horizontal: context.portrait ? 16.w : 8.w, vertical: context.portrait ? 24.w : 8.w),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(context.portrait ? 16.w : 8.w),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildLoading(),
                    context.portrait ? 16.gap : 4.gap,
                    Text(
                      loadText,
                      style: TextStyle(
                        color: CupertinoColors.white,
                        fontSize: context.portrait ? 24.w : 10.w,
                      ),
                    ),
                  ],
                ),
              )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildLoading() {
    return const CupertinoActivityIndicator(
      color: CupertinoColors.white,
    );
  }
}
