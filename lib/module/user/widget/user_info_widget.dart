import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/extension/obj_ext.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

/// author: Liu<PERSON><PERSON>
/// date: 2025-07-18
/// description: 用户信息组件

class UserInfoWidget extends ConsumerStatefulWidget {
  const UserInfoWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _UserInfoWidgetState();
}

class _UserInfoWidgetState extends ConsumerState<UserInfoWidget> {
  @override
  Widget build(BuildContext context) {
    final isLogin = ref.watch(loginProvider);
    if (!isLogin) return _buildNotLogin();
    return SizedBox(
      width: 702.w,
      child: Row(
        children: [
          _buildUserAvatar(),
          24.gap,
          Expanded(child: _buildUserInfo()),
        ],
      ),
    );
  }

  /// 未登录
  Widget _buildNotLogin() {
    return GestureDetector(
      onTap: () => context.push(ref.read(getLoginRouteProvider)),
      child: Container(
        width: 702.w,
        color: Colors.transparent,
        child: Row(
          children: [
            Container(
              width: 148.w,
              height: 148.w,
              decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white),
              child: Image.asset(
                'assets/images/my_center/default_head_icon.webp',
                width: 168.w,
                height: 48.w,
              ),
            ),
            24.gap,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '登录/注册',
                      style: TextStyle(
                        color: Color(0xff333333),
                        fontSize: 40.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    16.gap,
                    Icon(Icons.arrow_forward_ios_rounded, size: 36.w, color: Color(0xff333333)),
                  ],
                ),
                16.gap,
                Text(
                  '你还没有登录，登录后享受更多权益',
                  style: TextStyle(
                    color: Color(0xff666666),
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  /// 用户头像
  Widget _buildUserAvatar() {
    final user = ref.watch(userProvider).value!;
    return Container(
      width: 148.w,
      height: 148.w,
      clipBehavior: Clip.antiAlias,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 4.w),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(72.w),
        child: CQCachedNetworkImage(
          imageUrl: user.avatar ?? '',
          width: 144.w,
          height: 144.w,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  /// 用户信息
  Widget _buildUserInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUserName(),
        16.gap,
        _buildUserId(),
      ],
    );
  }

  Widget _buildUserName() {
    final user = ref.watch(userProvider).value!;
    return Row(
      children: [
        Flexible(
          child: Text(
            user.name.isBlank ? '健康达人' : user.name!,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              color: Color(0xff333333),
              fontSize: 40.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        if (user.isCompanionVip)
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Image.asset(
              'assets/images/my_center/icon_dietary_vip.webp',
              width: 168.w,
              height: 48.w,
            ),
          ),
      ],
    );
  }

  Widget _buildUserId() {
    final mid = SpUtil().get(spAppMid);
    return Row(
      children: [
        Text(
          'ID: $mid',
          style: TextStyle(
            color: Color(0xff999999),
            fontSize: 28.sp,
            fontWeight: FontWeight.w400,
          ),
        ),
        16.gap,
        GestureDetector(
          onTap: () {
            Clipboard.setData(ClipboardData(text: mid.toString()));
            Toast.show('用户ID复制成功');
          },
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF27D4B3), Color(0xFF61E48E)],
                begin: Alignment.bottomLeft,
                end: Alignment.topRight,
              ),
              borderRadius: BorderRadius.circular(30.r),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.w),
              child: Text(
                '复制',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
