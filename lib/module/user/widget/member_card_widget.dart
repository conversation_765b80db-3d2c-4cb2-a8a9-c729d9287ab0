import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/extension/obj_ext.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/module/user/route/user_route.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/fluwx_util.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: Liusilong
/// date: 2025-07-18
/// description: 会员卡组件

/// 会员卡组件
/// 非会员
/// 会员中
/// 会员到期
class MemberCardWidget extends ConsumerStatefulWidget {
  const MemberCardWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _MemberCardWidgetState();
}

class _MemberCardWidgetState extends ConsumerState<MemberCardWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 702.w,
      height: 200.w,
      child: Stack(
        children: [
          _buildBg(),
          _buildMemberInfo(),
        ],
      ),
    );
  }

  Widget _buildMemberInfo() {
    return Positioned(
      top: 0,
      left: 32.w,
      right: 32.w,
      child: SizedBox(
        height: 200.w,
        child: Row(
          children: [
            _buildLeft(),
            _buildRight(),
          ],
        ),
      ),
    );
  }

  Widget _buildRight() {
    final user = ref.watch(userProvider).value!;
    final String btnText;
    final String memberType;
    if (user.isCompanionVip) {
      btnText = "今日食谱";
      memberType = "有效期内会员";
    } else if (user.isCompanionVipExpired) {
      btnText = "立即续费";
      memberType = "已购买过不在有效期";
    } else {
      btnText = "立即购买";
      memberType = "从未购买会员";
    }
    return GestureDetector(
      onTap: () {
        final username = user.companionUserInfo?.miniGhid;
        final path = user.companionUserInfo?.miniPath;
        if (username.isBlank) {
          Toast.showMessage('未指定小程序id');
          return;
        }
        FluwxUtil().openMiniProgram(username!, path: path);
        AppBuryingPointUtil.click(modulePart: '178_cqjk_app_mine_00002', moduleOri: '食养会员', alternate: {
          "member_type": memberType,
          "buy_member_type": btnText,
        });
      },
      child: Container(
        width: 212.w,
        height: 80.w,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xffFFF2CF),
              Color(0xffE5BF6F),
            ],
          ),
          borderRadius: BorderRadius.circular(40.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ShaderMask(
              shaderCallback: (Rect bounds) {
                return LinearGradient(
                  colors: [Color(0xFF563508), Color(0xFF271804)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(bounds);
              },
              child: Text(
                btnText,
                style: TextStyle(
                  fontSize: 32.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
            4.gap,
            Image.asset(
              "assets/images/my_center/icon_arrow_1.png",
              width: 20.w,
              height: 20.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeft() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildTitle(),
          12.gap,
          _buildMemberStatus(),
          4.gap,
          _buildMemberInterests(),
        ],
      ),
    );
  }

  Widget _buildMemberStatus() {
    final user = ref.watch(userProvider).value!;
    final endTime = user.companionUserInfo?.endTime;
    final String statusText;
    if (user.isCompanionVip) {
      statusText = "有效期至 $endTime";
    } else if (user.isCompanionVipExpired) {
      statusText = "已于 $endTime 失效";
    } else {
      statusText = "惊喜福利\t限时特惠";
    }
    return Text(
      statusText,
      style: TextStyle(
        color: Color(0xffE5BE6F),
        fontSize: 28.sp,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget _buildBg() {
    final BorderRadius borderRadius;
    final user = ref.watch(userProvider).value!;
    if (user.isNutritionVip) {
      borderRadius = BorderRadius.vertical(top: Radius.circular(24.r));
    } else {
      borderRadius = BorderRadius.circular(24.r);
    }
    return Positioned(
      top: 0,
      left: 0,
      child: ClipRRect(
        borderRadius: borderRadius,
        child: Image.asset(
          'assets/images/my_center/img_member.webp',
          width: 702.w,
          height: 200.w,
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Image.asset('assets/images/my_center/img_member_txt.webp', height: 48.w);
  }

  /// 查看会员权益
  Widget _buildMemberInterests() {
    final user = ref.watch(userProvider).value!;
    if (user.isCompanionVip) return const SizedBox.shrink();
    return GestureDetector(
      onTap: () {
        MemberInterestRoute().push(context);
        AppBuryingPointUtil.click(modulePart: '178_cqjk_app_mine_00003', moduleOri: '食养会员-查看会员权益');
      },
      child: Row(
        children: [
          Text(
            "查看会员权益",
            style: TextStyle(
              color: Color(0xffFFF2CF),
              fontSize: 28.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
          14.gap,
          Icon(
            Icons.arrow_forward_ios_rounded,
            color: Color(0xffFFF2CF),
            size: 24.sp,
          ),
        ],
      ),
    );
  }
}
