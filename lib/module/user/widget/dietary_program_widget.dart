import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/fluwx_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: Liu<PERSON><PERSON>
/// date: 2025-07-18
/// description: 会员饮食方案

/// 会员饮食方案
class DietaryProgramWidget extends ConsumerStatefulWidget {
  const DietaryProgramWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<DietaryProgramWidget> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: 702.w,
          height: 287.w,
          decoration: BoxDecoration(
            color: Color(0xffFFF8E7),
            border: Border.all(
              color: Color(0xffffffff),
              width: 2.w,
            ),
            borderRadius: BorderRadius.circular(24.r),
            boxShadow: [
              BoxShadow(
                color: Color(0xff333333).withValues(alpha: 0.06),
                blurRadius: 24.r,
                offset: Offset(0, 8.w),
              ),
            ],
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              _buildInfo(),
              _buildSchedule(),
            ],
          ),
        ),
        _buildTeacherInfo(),
      ],
    );
  }

  Widget _buildTeacherInfo() {
    final request = ref.watch(userProvider);
    return request.when(
        data: (data) {
          return Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                if (data.nutritionPhase?.miniTeacherPath != null && data.nutritionPhase?.miniGhid != null) {
                  FluwxUtil().openMiniProgram(data.nutritionPhase?.miniGhid ?? "", path: data.nutritionPhase?.miniTeacherPath!);
                  AppBuryingPointUtil.click(modulePart: '178_cqjk_app_mine_00006', moduleOri: '	我的方案-专属营养师');
                }
              },
              child: Stack(
                children: [
                  Image.asset('assets/images/my_center/bg_teacher.webp', width: 360.w, height: 84.w),
                  Positioned(
                    top: 12.w,
                    left: 42.w,
                    child: SizedBox(
                      width: 308.w,
                      child: Row(
                        children: [
                          Image.asset('assets/images/my_center/icon_vip_teacher.webp', width: 28.w, height: 28.w),
                          12.gap,
                          Expanded(
                            child: Text(
                              "专属营养师\t${data.nutritionPhase?.teacherName}",
                              maxLines: 1,
                              overflow: TextOverflow.clip,
                              style: TextStyle(
                                color: Color(0xff291904),
                                fontSize: 28.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          Image.asset(
                            "assets/images/my_center/icon_arrow_1.png",
                            width: 24.w,
                            height: 24.w,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        error: (e, s) => SizedBox.shrink(),
        loading: () => SizedBox.shrink());
  }

  Widget _buildInfo() {
    final user = ref.watch(userProvider).value!;
    return Positioned(
      left: 24.w,
      top: 24.w,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "我的方案",
            style: TextStyle(
              color: Color(0xff333333),
              fontSize: 36.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          6.gap,
          Text(
            "${user.nutritionPhase?.templateName}",
            style: TextStyle(
              color: Color(0xff9F4A00),
              fontSize: 32.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 当前进度
  Widget _buildSchedule() {
    final user = ref.watch(userProvider).value!;
    return Positioned(
      bottom: 4.w,
      child: GestureDetector(
        onTap: () {
          if (user.nutritionPhase?.miniTemplatePath != null && user.nutritionPhase?.miniGhid != null) {
            FluwxUtil().openMiniProgram(user.nutritionPhase?.miniGhid ?? "", path: user.nutritionPhase?.miniTemplatePath!);
            AppBuryingPointUtil.click(modulePart: '178_cqjk_app_mine_00005', moduleOri: '我的方案-查看');
          }
        },
        child: Container(
          width: 688.w,
          height: 134.w,
          decoration: BoxDecoration(
            gradient: LinearGradient(colors: [
              Colors.white.withValues(alpha: 0.7),
              Colors.white,
            ]),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Row(
            children: [
              18.gap,
              _buildScheduleInfo(),
              _buildRight(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRight() {
    return Row(
      children: [
        20.gap,
        _buildDivider(),
        40.gap,
        Text(
          "查看",
          style: TextStyle(
            color: Color(0xff9F4A00),
            fontSize: 32.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        12.gap,
        Image.asset(
          "assets/images/my_center/icon_arrow_2.png",
          width: 24.w,
          height: 24.w,
        ),
        30.gap,
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      width: 2.w,
      height: 60.w,
      color: Color(0xff9F4A00).withValues(alpha: 0.15),
    );
  }

  Widget _buildScheduleInfo() {
    final user = ref.watch(userProvider).value!;
    return Expanded(
      child: SizedBox(
        height: 128.w,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                _buildScheduleTag(),
                12.gap,
                Expanded(
                  child: Text(
                    "${user.nutritionPhase?.currentPhaseName}",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Color(0xff333333),
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            4.gap,
            Text(
              "第 ${user.nutritionPhase?.currentWeek} 周，第 ${user.nutritionPhase?.currentDay} 天",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: Color(0xff999999),
                fontSize: 24.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleTag() {
    return Container(
      width: 100.w,
      height: 38.w,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xffFDF3D9),
            Color(0xffFCE29E),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        '当前进度',
        style: TextStyle(
          color: Color(0xff9F4A00),
          fontSize: 22.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
