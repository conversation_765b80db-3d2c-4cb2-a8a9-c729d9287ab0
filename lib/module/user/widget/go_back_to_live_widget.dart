import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: Liu<PERSON><PERSON>
/// date: 2025-08-07
/// description: 回到直播组件，用于从直播间跳转到的有赞商品详情页面

class GoBackToLiveWidget extends ConsumerStatefulWidget {
  const GoBackToLiveWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _GoBackToLiveWidgetState();
}

class _GoBackToLiveWidgetState extends ConsumerState<GoBackToLiveWidget> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 136.w,
      height: 136.w,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Positioned(
            child: Container(
              width: 120.w,
              height: 120.w,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: Color(0xffFF6D2E), width: 4.w),
                borderRadius: BorderRadius.circular(60.w),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    "assets/images/cq_app_logo_trans.png",
                    width: 90.w,
                    height: 90.w,
                    fit: BoxFit.cover,
                  ),
                  Container(
                    width: 108.w,
                    height: 108.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.black.withValues(alpha: 0.5),
                    ),
                  ),
                  _LiveStatusWidget(),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            child: Container(
              width: 136.w,
              height: 40.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.w),
                gradient: LinearGradient(
                  colors: [Color(0xffFF8B4F), Color(0xffFF2C3D)],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xffFF313E).withValues(alpha: 0.4),
                    blurRadius: 12.w,
                    offset: Offset(0, 5.w),
                  ),
                ],
              ),
              child: Center(
                child: Text(
                  "返回直播间",
                  style: TextStyle(
                    fontSize: 23.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}


/// 直播状态
class _LiveStatusWidget extends StatelessWidget {
  const _LiveStatusWidget();

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}