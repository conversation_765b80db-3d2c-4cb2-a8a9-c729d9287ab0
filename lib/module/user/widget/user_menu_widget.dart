import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/extension/obj_ext.dart';
import 'package:changqing_health_app/module/agreement/web_page/routes/agreement_webview_routes.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart';
import 'package:changqing_health_app/module/other/check_version/app_upgrade_dialog.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/module/user/route/user_route.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

/// author: Liu<PERSON><PERSON>
/// date: 2025-07-21
/// description: 用户菜单组件

/// 我的页面 菜单
class UserMenuWidget extends ConsumerStatefulWidget {
  const UserMenuWidget({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _UserMenuWidgetState();
}

class _UserMenuWidgetState extends ConsumerState<UserMenuWidget> {
  @override
  Widget build(BuildContext context) {
    // 是否在审核中
    final isReview = ref.read(appVersionProviderProvider.notifier).isReview;
    return SizedBox(
      width: 702.w,
      child: Column(
        children: [
          if (!isReview) _MenuItem(icon: "icon_user_mall.png", title: "健康商城", onTap: _toYouZanMall, isFirst: !isReview),
          _MenuItem(icon: "user_agreement_icon.png", title: "用户协议", onTap: () => context.push(AgreementWebviewRoute().location), isFirst: isReview),
          _MenuItem(icon: "privacy_agreement_icon.png", title: "隐私协议", onTap: () => context.push(PrivacyPolicyWebviewRoute().location)),
          _MenuItem(icon: "report_icon.png", title: "投诉举报", onTap: () => context.push(UserReportRoute().location)),
          _MenuItem(icon: "phone_icon.png", title: "服务热线", onTap: () => launchUrl(Uri.parse('tel:$servicePhone'))),
          _MenuItem(icon: "version_icon_icon.png", title: "当前版本", onTap: _checkForUpgrade),
          _MenuItem(icon: "account_icon.png", title: "注销账号", isLast: true, onTap: () => context.push(UserDeregisterAccountRoute().location)),
        ],
      ),
    );
  }

  /// 跳转到健康商城
  void _toYouZanMall() {
    final isLogin = ref.read(loginProvider);
    if (!isLogin) {
      context.push(ref.read(getLoginRouteProvider));
    } else {
      final user = ref.read(userProvider).valueOrNull;
      if (user == null) return;
      if (!user.yzHujWebUrl.isBlank) {
        YouzanMallRoute(url: user.yzHujWebUrl!, title: "健康商城").push(context);
      } else {
        Toast.showMessage("商城链接暂不可用");
      }
    }
  }

  /// 检查版本更新
  void _checkForUpgrade() {
    if (ref.read(appVersionProviderProvider.notifier).isNewVersion) {
      AppUpgradeDialog.show(context, ref);
    } else {
      Toast.showMessage("当前已是最新版本");
    }
  }
}

class _MenuItem extends ConsumerWidget {
  final String icon;
  final String title;
  final VoidCallback? onTap;
  final bool? isFirst;
  final bool? isLast;
  const _MenuItem({
    super.key,
    required this.icon,
    required this.title,
    this.onTap,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LayoutBuilder(builder: (context, cons) {
      return GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          width: cons.maxWidth,
          height: 106.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: isFirst == true
                ? BorderRadius.only(
                    topLeft: Radius.circular(24.w),
                    topRight: Radius.circular(24.w),
                  )
                : isLast == true
                    ? BorderRadius.only(
                        bottomLeft: Radius.circular(24.w),
                        bottomRight: Radius.circular(24.w),
                      )
                    : null,
          ),
          child: Column(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Image.asset("assets/images/my_center/$icon", width: 42.w),
                    16.gap,
                    Text(
                      title,
                      style: TextStyle(
                        color: Color(0xFF333333),
                        fontSize: 32.sp,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Spacer(),
                    Icon(Icons.arrow_forward_ios_rounded, size: 32.w, color: Color(0xFF999999)),
                  ],
                ),
              ),
              if (isLast != true)
                Container(
                  height: 2.w,
                  color: Color(0xffF3F3F3),
                ),
            ],
          ),
        ),
      );
    });
  }
}
