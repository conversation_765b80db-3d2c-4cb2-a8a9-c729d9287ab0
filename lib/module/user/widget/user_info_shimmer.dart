import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2025-07-30
/// description: 用户信息骨架屏

class UserInfoShimmer extends StatelessWidget {
  const UserInfoShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          width: context.sw,
          height: 388.w,
        ),
        Positioned(
          top: 0,
          left: 0,
          child: Image.asset("assets/images/my_center/bg_user_short.webp", width: context.sw, height: 388.w),
        ),
        Positioned(
          top: 200.w,
          left: 24.w,
          child: SizedBox(
            width: 702.w,
            child: Row(
              children: [
                Container(
                  width: 148.w,
                  height: 148.w,
                  clipBehavior: Clip.antiAlias,
                  decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white.withValues(alpha: 0.5)),
                ),
                24.gap,
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      color: Colors.white.withValues(alpha: 0.5),
                      width: 200.w,
                      height: 32.w,
                    ),
                    16.gap,
                    Container(
                      color: Colors.white.withValues(alpha: 0.5),
                      width: 300.w,
                      height: 32.w,
                    ),
                  ],
                )),
              ],
            ),
          ),
        )
      ],
    );
  }
}
