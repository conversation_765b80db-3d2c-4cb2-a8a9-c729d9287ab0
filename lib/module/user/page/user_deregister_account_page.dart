import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/other/config/app_debug_config_page.dart';
import 'package:changqing_health_app/module/user/provider/user_deregister_account_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// 注销账号页面
class UserDeregisterAccountPage extends ConsumerStatefulWidget {
  const UserDeregisterAccountPage({super.key});

  @override
  ConsumerState<UserDeregisterAccountPage> createState() => _UserDeregisterAccountPageState();
}

class _UserDeregisterAccountPageState extends ConsumerState<UserDeregisterAccountPage> {
  int _tapNumbers = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("注销账号")),
      backgroundColor: Colors.white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: double.maxFinite),
          GestureDetector(
            onTap: () {
              if (_tapNumbers > 20) {
                _tapNumbers = 0;
                Future.delayed(const Duration(seconds: 2), () {
                  if (context.mounted) {
                    context.push(AppDebugConfigRoute().location);
                  }
                });
                return;
              }
              _tapNumbers++;
              int tempTapNumber = _tapNumbers;
              Future.delayed(const Duration(milliseconds: 800), () {
                if (tempTapNumber == _tapNumbers) {
                  _tapNumbers = 0;
                }
              });
            },
            child: Container(
              color: Colors.transparent,
              child: Column(
                children: [
                  49.gap,
                  Text("温馨提示", style: TextStyle(fontSize: 17, fontWeight: FontWeight.w700, color: Colors.black.withOpacity(0.8))),
                  12.gap,
                  Text("账号一旦注销，将无法恢复", style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.black.withOpacity(0.8))),
                  12.gap,
                  _itemWidget(content: "您仅可注销您本人的账户"),
                  12.gap,
                  _itemWidget(content: "所有账户信息将被删除，包括历史记录、聊天数据、个人数据"),
                  12.gap,
                  _itemWidget(content: "注销后，您将无法恢复账户"),
                  12.gap,
                  _itemWidget(content: "有任何疑问，请咨询服务热线$servicePhone"),
                  12.gap,
                  _itemWidget(content: "注销需要一定周期，请您耐心等待"),
                  24.gap,
                ],
              ),
            ),
          ),
          // 继续注销按钮
          GestureDetector(
            onTap: () {
              HapticFeedback.mediumImpact();
              showCupertinoDialog(
                context: context,
                builder: (dialogContext) => CupertinoAlertDialog(
                  title: Text("确定要注销账户吗？"),
                  actions: [
                    CupertinoDialogAction(
                      onPressed: () async {
                        dialogContext.pop();
                        await Future.delayed(Duration(milliseconds: 300));
                        context.pop();
                      },
                      child: Text("取消"),
                    ),
                    CupertinoDialogAction(
                      onPressed: () {
                        dialogContext.pop();
                        _deregisterAccountSuccess();
                      },
                      child: Text("确定"),
                    ),
                  ],
                ),
              );
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 24),
              width: double.maxFinite,
              height: 44,
              alignment: Alignment.center,
              decoration: BoxDecoration(color: Colors.red, borderRadius: BorderRadius.circular(12)),
              child: Text("继续注销", style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _itemWidget({required String content}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        24.gap,
        Container(
          margin: EdgeInsets.only(top: 8),
          width: 6,
          height: 6,
          decoration: BoxDecoration(color: Colors.red, shape: BoxShape.circle),
        ),
        12.gap,
        Expanded(child: Text(content, style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: Colors.black.withOpacity(0.7)))),
        24.gap,
      ],
    );
  }

  Future<void> _deregisterAccountSuccess() async {
    await Future.delayed(Duration(milliseconds: 300));
    showCupertinoDialog(
      context: context,
      builder: (dialogContext) {
        return CupertinoAlertDialog(
          content: Text("注销账号需要一定周期，请您耐心等待"),
          actions: [
            CupertinoDialogAction(
              onPressed: () async {
                await ref.read(userDeregisterAccountProvider.notifier).deregisterAccount();
                await ref.read(loginProvider.notifier).logout();
                dialogContext.pop();
                await Future.delayed(Duration(milliseconds: 300));
                context.pop();
              },
              child: Text("确定"),
            ),
          ],
        );
      },
    );
  }
}
