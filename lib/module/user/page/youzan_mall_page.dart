import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/module/user/widget/go_back_to_live_widget.dart';
import 'package:cq_youzan_webview/cq_youzan_controller.dart';
import 'package:cq_youzan_webview/cq_youzan_webview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: Liusilong
/// date: 2025-08-06
/// description: 有赞商城页面
class YouzanMallPage extends ConsumerStatefulWidget {
  // 商城url
  final String url;
  // 页面标题
  final String? title;
  const YouzanMallPage({required this.url, this.title, super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _YouzanMallPageState();
}

class _YouzanMallPageState extends ConsumerState<YouzanMallPage> {
  late CQYouZanController _controller;

  void _loadUrl() {
    _controller.loadUrl(url: widget.url);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) return;
        final canGoBack = await _controller.canGoBack();
        if (canGoBack) {
          _controller.goBack();
        } else {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.title ?? '有赞商城'),
          actions: [
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
        body: SizedBox(
          width: context.sw,
          height: context.sh,
          child: Stack(
            children: [
              Positioned.fill(
                child: CQYouZanWebView(
                  onCreated: (controller) {
                    _controller = controller;
                    _loadUrl();
                  },
                ),
              ),
              Positioned(
                top: 328.w,
                right: 24.w,
                child: GoBackToLiveWidget(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
