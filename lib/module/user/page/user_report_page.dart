import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/user/provider/user_report_provider.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:go_router/go_router.dart';

class UserReportPage extends ConsumerStatefulWidget {
  const UserReportPage({super.key});

  @override
  ConsumerState<UserReportPage> createState() => _UserReportPageState();
}

class _UserReportPageState extends ConsumerState<UserReportPage> {
  @override
  Widget build(BuildContext context) {
    ref.watch(userReportTextEditControllerProvider);
    ref.watch(userReportContactEditControllerProvider);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text("用户举报"),
        ),
        resizeToAvoidBottomInset: false,
        body: SingleChildScrollView(
          child: Column(
            children: [
              _titleItemWidget(title: "投诉类型", child: _typeContentWidget()),
              12.gap,
              _titleItemWidget(title: "描述投诉内容", child: _describeContentWidget()),
              12.gap,
              _contactContentWidget(),
              12.gap,
              _submitButtonWidget(),
              SizedBox(height: MediaQuery.of(context).viewInsets.bottom * 1.1),
            ],
          ),
        ),
      ),
    );
  }

  /// 提交按钮
  Widget _submitButtonWidget() {
    String phone = ref.watch(userReportContactEditControllerProvider).text;
    bool isEnable = ref.watch(userReportTextEditControllerProvider).text.isNotEmpty &&
        phone.isNotEmpty && phone.length == 11 && 
        ref.watch(userReportSelectIndexStateProvider).isNotEmpty;
    return GestureDetector(
      onTap: () async {
        if (!isEnable) return;
        bool success = await ref.read(userReportApiProvider.future);
        if (success) {
          if (mounted) {
            context.pop();
          }
          Toast.show("我们已收到您的举报，会尽快处理");
        } else {
          Toast.show("提交失败，请稍后再试");
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 100),
        width: MediaQuery.of(context).size.width * 0.8,
        height: 44,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: isEnable ? Colors.blueAccent : const Color.fromARGB(255, 212, 209, 209),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Text('提交', style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.white)),
      ),
    );
  }

  /// 投诉类型widget
  Widget _typeContentWidget() {
    return SizedBox(
      width: double.infinity,
      child: Wrap(
        spacing: 12,
        runSpacing: 12,
        children: ref.watch(userReportTypeListProvider).value?.map((e) => _typeItemWidget(title: e)).toList() ?? [],
      ),
    );
  }

  /// 描述投诉内容
  Widget _describeContentWidget() {
    int textLength = ref.read(userReportTextEditControllerProvider).text.length;
    return Container(
      color: Colors.transparent,
      height: 120,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: TextField(
              scrollPadding: const EdgeInsets.all(0),
              controller: ref.read(userReportTextEditControllerProvider),
              maxLength: 500,
              maxLines: 11111,
              decoration: InputDecoration(
                contentPadding: EdgeInsets.zero,
                hintText: "请描述投诉内容",
                hintStyle: const TextStyle(fontSize: 12, color: Colors.grey),
                border: InputBorder.none,
                // 去掉textfield最大值的 提示
                counterText: "",
              ),
              style: const TextStyle(fontSize: 12),
              onChanged: (text) {
                setState(() {});
              },
            ),
          ),
          4.gap,
          Text("$textLength/500", style: const TextStyle(fontSize: 10, color: Colors.grey)),
        ],
      ),
    );
  }

  /// 联系方式
  Widget _contactContentWidget() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text("联系方式", style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.black)),
          12.gap,
          Expanded(
            child: TextField(
              scrollPadding: const EdgeInsets.all(0),
              controller: ref.read(userReportContactEditControllerProvider),
              maxLines: 1,
              maxLength: 11,
              decoration: InputDecoration(
                contentPadding: EdgeInsets.zero,
                hintText: "请输入联系方式",
                hintStyle: const TextStyle(fontSize: 12, color: Colors.grey),
                border: InputBorder.none,
                counterText: "",
              ),
              style: const TextStyle(fontSize: 12),
              onChanged: (text) {
                setState(() {});
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _titleItemWidget({required String title, required Widget child}) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: Colors.black)),
          12.gap,
          child,
        ],
      ),
    );
  }

  Widget _typeItemWidget({required String title}) {
    return GestureDetector(
      onTap: () {
        ref.read(userReportSelectIndexStateProvider.notifier).state = title;
      },
      child: LayoutBuilder(builder: (context, constraints) {
        bool isSelect = ref.watch(userReportSelectIndexStateProvider) == title;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: (constraints.maxWidth - 12) / 2,
          padding: const EdgeInsets.symmetric(vertical: 8),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelect ? const Color.fromARGB(255, 236, 245, 253) : const Color.fromARGB(255, 246, 246, 246),
            borderRadius: BorderRadius.circular(6),
            border: Border.all(color: isSelect ? Colors.blue : Colors.transparent),
          ),
          child: Text(title, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w700, color: isSelect ? Colors.blue : Colors.black.withOpacity(0.8))),
        );
      }),
    );
  }
}
