import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/user/page/user_page_for_release.dart';
import 'package:changqing_health_app/module/user/page/user_page_for_review.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 用户页面
class UserPage extends ConsumerStatefulWidget {
  const UserPage({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<UserPage> {
  @override
  Widget build(BuildContext context) {
    return switch (ref.read(appVersionProviderProvider.notifier).isReview) {
      true => UserPageForReview(),
      false => UserPageForRelease(),
    };
  }
}
