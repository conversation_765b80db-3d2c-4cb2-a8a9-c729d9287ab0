import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/module/user/widget/dietary_program_widget.dart';
import 'package:changqing_health_app/module/user/widget/member_card_widget.dart';
import 'package:changqing_health_app/module/user/widget/user_info_shimmer.dart';
import 'package:changqing_health_app/module/user/widget/user_info_widget.dart';
import 'package:changqing_health_app/module/user/widget/user_menu_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// author: Liusilong
/// date: 2025-07-18
/// description: 用户页面: 用于发布模式

class UserPageForRelease extends ConsumerStatefulWidget {
  const UserPageForRelease({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<UserPageForRelease> {
  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
      child: Scaffold(
        extendBodyBehindAppBar: true,
        body: SingleChildScrollView(
          child: Column(
            children: [
              _buildUserInfoAndMemberCard(),
              _buildMenus(),
              _buildLogout(),
            ],
          ),
        ),
      ),
    );
  }

  /// 用户信息和会员卡
  Widget _buildUserInfoAndMemberCard() {
    final isLogin = ref.watch(loginProvider);
    if (!isLogin) {
      return SizedBox(
        height: 388.w,
        child: Stack(
          children: [
            _buildTopBg(),
            _buildUserInfo(),
          ],
        ),
      );
    } else {
      final userRequest = ref.watch(userProvider);
      return userRequest.maybeWhen(
        data: (user) {
          double boxHeight = 388.w;
          if (user.isGuest) {
            // 不展示陪伴营，不展示修复营
            boxHeight = 388.w;
          } else if ((user.isCompanionVip || user.isCompanionVipExpired) && !user.isNutritionVip) {
            // 展示陪伴营，不展示修复营
            boxHeight = 580.w;
          } else if (user.companionUserInfo == null && user.isNutritionVip) {
            // 不展示陪伴营，展示修复营
            boxHeight = 667.w;
          } else {
            // 展示陪伴营，展示修复营
            boxHeight = 845.w;
          }

          return SizedBox(
            height: boxHeight,
            child: Stack(
              children: [
                _buildTopBg(),
                _buildUserInfo(),
                Positioned(
                  bottom: 0,
                  child: _buildMemberInfo(),
                ),
              ],
            ),
          );
        },
        orElse: () => UserInfoShimmer(),
      );
    }
  }

  /// 会员卡片 + 饮食方案
  Widget _buildMemberInfo() {
    final isLogin = ref.watch(loginProvider);
    if (!isLogin) return SizedBox.shrink();
    final user = ref.watch(userProvider).value!;
    if (user.isGuest) return const SizedBox.shrink();
    final double boxHeight;
    if ((user.isCompanionVip || user.isCompanionVipExpired) && !user.isNutritionVip) {
      // 展示陪伴营，不展示修复营
      boxHeight = 200.w;
    } else if (user.companionUserInfo == null && user.isNutritionVip) {
      // 不展示陪伴营，展示修复营
      boxHeight = 287.w;
    } else {
      // 展示陪伴营，展示修复营
      boxHeight = 465.w;
    }
    return SizedBox(
      height: boxHeight,
      width: context.sw,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          if (user.companionUserInfo != null)
            Positioned(
              top: 0,
              child: MemberCardWidget(),
            ),
          if (user.isNutritionVip)
            Positioned(
              bottom: 0,
              child: DietaryProgramWidget(),
            ),
        ],
      ),
    );
  }

  /// 顶部背景
  /// 图片高度：未登录 388; 登录 460
  Widget _buildTopBg() {
    final String bgPath;
    final double imageHeight;
    final isLogin = ref.watch(loginProvider);
    if (isLogin) {
      final user = ref.watch(userProvider).value!;
      if (user.isGuest) {
        bgPath = 'assets/images/my_center/bg_user_short.webp';
        imageHeight = 388.w;
      } else {
        bgPath = 'assets/images/my_center/back_icon.webp';
        imageHeight = 460.w;
      }
    } else {
      bgPath = 'assets/images/my_center/bg_user_short.webp';
      imageHeight = 388.w;
    }
    return Positioned(
      left: 0,
      top: 0,
      child: Image.asset(
        bgPath,
        height: imageHeight,
        width: context.sw,
        fit: BoxFit.cover,
      ),
    );
  }

  Widget _buildUserInfo() {
    return Positioned(
      top: 200.w,
      left: 24.w,
      child: UserInfoWidget(),
    );
  }

  /// 菜单
  Widget _buildMenus() {
    final isLogin = ref.watch(loginProvider);
    if (!isLogin) {
      return Padding(
        padding: EdgeInsets.only(top: 0, bottom: 24.w),
        child: UserMenuWidget(),
      );
    } else {
      return Consumer(builder: (context, ref, child) {
        final user = ref.watch(userProvider);
        return Column(
          children: [
            user.maybeWhen(data: (user) => user.isGuest ? 0.gap : 24.gap, orElse: () => 0.gap),
            UserMenuWidget(),
            24.gap,
          ],
        );
      });
    }
  }

  /// 退出登录
  Widget _buildLogout() {
    final isLogin = ref.watch(loginProvider);
    if (!isLogin) return SizedBox.shrink();
    return GestureDetector(
      onTap: _logout,
      child: Container(
        margin: EdgeInsets.only(bottom: 24.w),
        width: 702.w,
        height: 94.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24.w),
        ),
        alignment: Alignment.center,
        child: Text(
          '退出登录',
          style: TextStyle(
            color: Color(0xFF999999),
            fontSize: 28.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _logout() async {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          content: Text('确定要退出登录吗?'),
          actions: <CupertinoDialogAction>[
            CupertinoDialogAction(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
              },
            ),
            CupertinoDialogAction(
              isDestructiveAction: true,
              onPressed: () {
                ref.read(loginProvider.notifier).logout();
                Navigator.of(context).pop();
                // 在这里处理确定逻辑
              },
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
