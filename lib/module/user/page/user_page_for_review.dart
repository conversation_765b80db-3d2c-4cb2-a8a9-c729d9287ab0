import 'package:cached_network_image/cached_network_image.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/extension/widget_ext.dart';
import 'package:changqing_health_app/module/agreement/web_page/routes/agreement_webview_routes.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/route/login_route.dart';
import 'package:changqing_health_app/module/other/check_version/app_upgrade_dialog.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/module/user/route/user_route.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

/// 用户页面: 用于审核模式
class UserPageForReview extends ConsumerStatefulWidget {
  const UserPageForReview({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _State();
}

class _State extends ConsumerState<UserPageForReview> {
  void _logout() async {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          content: Text('确定要退出登录吗?'),
          actions: <CupertinoDialogAction>[
            CupertinoDialogAction(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
              },
            ),
            CupertinoDialogAction(
              child: Text('确定'),
              isDestructiveAction: true,
              onPressed: () {
                ref.read(loginProvider.notifier).logout();
                Navigator.of(context).pop();
                // 在这里处理确定逻辑
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(loginProvider);
    ref.watch(userProvider);
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: _bodyWidget(),
      // body: Column(
      //   children: [
      //     Expanded(
      //       child: SingleChildScrollView(
      //         child: Column(
      //           children: [
      //             _buildHeader(),
      //             SizedBox(height: 24.w),
      //             _buildFooter(),
      //             SizedBox(height: 24.w),
      //             _referenceLink(),
      //             SizedBox(height: 24.w),
      //             _buildContact(),
      //             SizedBox(height: 24.w),
      //             12.gap,
      //             _buildDeregisterAccount(),
      //           ],
      //         ),
      //       ),
      //     ),
      //     _buildLogout(),
      //     SizedBox(height: 24.w),
      //   ],
      // ),
    );
  }

  Widget _bodyWidget() {
    return Stack(
      children: [
        Container(width: double.maxFinite, height: double.maxFinite, color: Color(0xFFF0F0F0)),
        Positioned(child: Image.asset('assets/images/my_center/back_icon.webp', height: 460.w)),
        Positioned(left: 24.w, right: 24.w, top: 120.w + kToolbarHeight, child: _buildHeader()),
        Positioned(
            left: 24.w,
            right: 24.w,
            top: 120.w + kToolbarHeight + 148.w + 48.w,
            child: Column(
              children: [
                _buildWrapWidget(),
                SizedBox(height: 24.w),
                _buildLogout(),
              ],
            )),
      ],
    );
  }

  Widget _buildHeader() {
    bool isLogin = ref.watch(loginProvider);
    if (!isLogin) {
      return GestureDetector(
        onTap: () {
          context.push(ref.read(getLoginRouteProvider));
        },
        child: Container(
          color: Colors.transparent,
          child: Row(
            children: [
              SizedBox(
                width: 148.w,
                height: 148.w,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20.w),
                  child: Image.asset('assets/images/my_center/default_head_icon.webp'),
                ),
              ),
              SizedBox(width: 24.w),
              Expanded(
                child: SizedBox(
                  height: 148.w,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("点击登录/注册", style: TextStyle(color: Colors.black, fontWeight: FontWeight.w700, fontSize: 40.sp)),
                      Expanded(child: SizedBox.shrink()),
                      Text(
                        '班级: 暂无班级',
                        style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp),
                      ),
                      Text('学号: ---', style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      return ref.watch(userProvider).when(
            data: (data) {
              return Container(
                color: Colors.transparent,
                child: Row(
                  children: [
                    SizedBox(
                      width: 148.w,
                      height: 148.w,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20.w),
                        child: CQCachedNetworkImage(imageUrl: data.avatar ?? ''),
                      ),
                    ),
                    SizedBox(width: 24.w),
                    Expanded(
                      child: SizedBox(
                        height: 148.w,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(data.name ?? '', style: TextStyle(color: Colors.black, fontWeight: FontWeight.w700, fontSize: 40.sp)),
                            Expanded(child: SizedBox.shrink()),
                            Text(
                              '班级: 暂无班级',
                              style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp),
                            ),
                            Text('学号: ${SpUtil().get(spAppMid)}', style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
            error: (e, s) => SizedBox.shrink(),
            loading: () => SizedBox.shrink(),
          );
    }
    // String name = "点击登录/注册";
    // String classId = "---";
    // String? avatar;
    // String mid = '---';
    // if (isLogin) {
    //   final user = ref.watch(userProvider);
    //   if (user.name != null && user.name != "") {
    //     name = user.name!;
    //   } else {
    //     name = "健康达人";
    //   }
    //   // classId = user.userClass?.className ?? "暂无班级";
    //   classId = "暂无班级";
    //   int? spMid = SpUtil().get(spAppMid);
    //   mid = spMid == null ? '---' : spMid.toString();
    //   avatar = user.avatar;
    // }
    // return GestureDetector(
    //   onTap: () {
    //     if (!isLogin) {
    //       context.push(ref.read(getLoginRouteProvider));
    //     }
    //   },
    //   child: Container(
    //     color: Colors.transparent,
    //     child: Row(
    //       children: [
    //         SizedBox(
    //           width: 148.w,
    //           height: 148.w,
    //           child: ClipRRect(
    //             borderRadius: BorderRadius.circular(20.w),
    //             child: isLogin && (avatar != null) ? CQCachedNetworkImage(imageUrl: avatar) : Image.asset('assets/images/my_center/default_head_icon.webp'),
    //           ),
    //         ),
    //         SizedBox(width: 24.w),
    //         Expanded(
    //           child: SizedBox(
    //             height: 148.w,
    //             child: Column(
    //               crossAxisAlignment: CrossAxisAlignment.start,
    //               children: [
    //                 Text(name, style: TextStyle(color: Colors.black, fontWeight: FontWeight.w700, fontSize: 40.sp)),
    //                 Expanded(child: SizedBox.shrink()),
    //                 Text(
    //                   ref.read(appVersionProviderProvider.notifier).isReview ? '' : '班级: $classId',
    //                   style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp),
    //                 ),
    //                 Text('学号: $mid', style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp)),
    //               ],
    //             ),
    //           ),
    //         ),
    //       ],
    //     ),
    //   ),
    // );
  }

  Widget _buildWrapWidget() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(24.w),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(vertical: 16.w),
        child: Wrap(
          children: [
            _buildItemWidget(title: '用户协议', path: 'user_agreement_icon.png', onTap: () => context.push(AgreementWebviewRoute().location)),
            _buildItemWidget(title: '隐私协议', path: 'privacy_agreement_icon.png', onTap: () => context.push(PrivacyPolicyWebviewRoute().location)),
            // _buildItemWidget(title: '数据来源', path: 'data_sources_icon.png', onTap: () => context.push(ReferenceLinkRoute().location)),
            _buildItemWidget(title: '投诉举报', path: 'report_icon.png', onTap: () => context.push(UserReportRoute().location)),
            _buildItemWidget(title: '服务热线', path: 'phone_icon.png', onTap: () => launchUrl(Uri.parse('tel:$servicePhone'))),
            _buildItemWidget(
                title: '检查版本',
                path: 'version_icon_icon.png',
                onTap: () {
                  if (ref.watch(appVersionProviderProvider.notifier).isNewVersion) {
                    AppUpgradeDialog.show(context, ref);
                  } else {
                    Toast.showMessage("当前已是最新版本");
                  }
                }),
            _buildItemWidget(title: '注销账号', path: 'account_icon.png', onTap: () => context.push(UserDeregisterAccountRoute().location)),
          ],
        ),
      ),
    );
  }

  Widget _buildItemWidget({required String title, required String path, required GestureTapCallback onTap}) {
    double width = (1.sw - 24.w * 2 - 3) / 4;
    return Container(
      color: Colors.transparent,
      width: width,
      height: 160.w - 16.w,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset('assets/images/my_center/$path', width: 56.w),
          SizedBox(height: 8.w),
          Text(
            title,
            style: TextStyle(color: Color(0xFF333333), fontSize: 28.sp, fontFamily: 'Helvetica'),
          ),
        ],
      ),
    ).onTap(onTap);
  }

  Widget _buildLogout() {
    bool isLogin = ref.watch(loginProvider);
    if (!isLogin) {
      return Container();
    }
    return Column(
      children: [
        GestureDetector(
          onTap: _logout,
          child: Container(
            height: 94.w,
            margin: EdgeInsets.symmetric(horizontal: 0.w),
            alignment: Alignment.center,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(24.w), color: Colors.white),
            child: Text(
              '退出登录',
              style: TextStyle(color: Color(0xFF999999), fontSize: 28.sp, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        SizedBox(height: 48.w),
      ],
    );
  }
}
