import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/h5liveroom/util/h5_scheme_manager.dart';
import 'package:changqing_health_app/module/user/page/reference_link_article_page.dart';
import 'package:changqing_health_app/module/user/route/user_route.dart';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

class ReferenceLinkPage extends ConsumerStatefulWidget {
  const ReferenceLinkPage({super.key});

  @override
  ConsumerState<ReferenceLinkPage> createState() => _ReferenceLinkPageState();
}

class _ReferenceLinkPageState extends ConsumerState<ReferenceLinkPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('参考链接'),
      ),
      body: Column(
        children: [
          _buildItemWidget(title: '中国营养学会（CNS）', content: 'https://www.cnsoc.org/latesachie/', onTap: () {
            launchUrl(Uri.parse("https://www.cnsoc.org/latesachie/"));
          }),
          _buildItemWidget(title: '国家卫生健康委员会', content: 'http://www.chinacdc.cn', onTap: () {
            launchUrl(Uri.parse("https://www.cnsoc.org/latesachie/"));
          }),
          _buildItemWidget(title: '科学文献', content: '来源于权威医学文献, 饮食建议均有据可依', onTap: () {
            context.push(ReferenceLinkArticleRoute().location);
          }),
        ],
      ),
    );
  }


  Widget _buildItemWidget({required String title, required String content, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: double.maxFinite,
        child: Container(
          margin: EdgeInsets.symmetric(vertical: 24.w, horizontal: 24.w),
          padding: EdgeInsets.symmetric(vertical: 24.w, horizontal: 24.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24.w),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: TextStyle(color: Colors.black, fontSize: 32.sp, fontWeight: FontWeight.w700)),
              12.gap,
              Text(content, style: TextStyle(color: Colors.black.withAlpha(160), fontSize: 28.sp)),
            ],
          ),
        ),
      ),
    );
  }
}
