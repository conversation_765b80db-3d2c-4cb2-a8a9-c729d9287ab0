import 'package:cached_network_image/cached_network_image.dart';
import 'package:changqing_health_app/extension/context_ext.dart';
import 'package:changqing_health_app/extension/obj_ext.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/util/cq_burying_point_util.dart';
import 'package:changqing_health_app/util/cq_cached_network_image.dart';
import 'package:changqing_health_app/util/fluwx_util.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

/// author: Liusilong
/// date: 2025-07-21
/// description: 会员权益页面

class MemberInterestPage extends ConsumerStatefulWidget {
  const MemberInterestPage({super.key});

  @override
  ConsumerState<MemberInterestPage> createState() => _MemberInterestPageState();
}

class _MemberInterestPageState extends ConsumerState<MemberInterestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.white.withValues(alpha: 0),
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            context.pop();
          },
          icon: Icon(Icons.arrow_back_ios_new_rounded, color: Color(0xff333333)), // 返回按钮设为白色
        ),
      ),
      body: Stack(
        children: [
          _buildImage(),
          _buildBottom(),
        ],
      ),
    );
  }

  Widget _buildImage() {
    final user = ref.watch(userProvider).value!;
    return Positioned(
      bottom: 152.w,
      top: 0,
      left: 0,
      child: SingleChildScrollView(
        child: CQCachedNetworkImage(
          imageUrl: user.companionUserInfo?.rightUrl ?? '',
          width: context.sw,
        ),
      ),
    );
  }

  Widget _buildBottom() {
    final user = ref.watch(userProvider).value!;
    return Positioned(
      bottom: 0,
      child: SafeArea(
        child: GestureDetector(
          onTap: () {
            final username = user.companionUserInfo?.miniGhid;
            final path = user.companionUserInfo?.miniPath;
            if (username.isBlank) {
              Toast.showMessage('未指定小程序id');
              return;
            }
            FluwxUtil().openMiniProgram(username!, path: path);
            AppBuryingPointUtil.click(modulePart: '178_cqjk_app_mine_00004', moduleOri: '食养会员-查看会员权益');
          },
          child: Container(
            width: context.sw,
            height: 152.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32.r),
                topRight: Radius.circular(32.r),
              ),
              gradient: LinearGradient(
                colors: [
                  Color(0xffFFF9EA),
                  Color(0xffFFFFFF),
                ],
              ),
            ),
            alignment: Alignment.center,
            child: Container(
              width: 702.w,
              height: 120.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(60.r),
                gradient: LinearGradient(
                  colors: [
                    Color(0xff271804),
                    Color(0xff563508),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Color(0x00ffd55f).withValues(alpha: 0.3),
                    blurRadius: 16.r,
                    offset: Offset(0, 6.w),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                        colors: [Color(0xFFFFF2CF), Color(0xFFE5BF6F)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds);
                    },
                    child: Text(
                      '请打开长轻食养小程序购买',
                      style: TextStyle(
                        fontSize: 42.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  ShaderMask(
                    shaderCallback: (Rect bounds) {
                      return LinearGradient(
                        colors: [Color(0xFFFFF2CF), Color(0xFFE5BF6F)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ).createShader(bounds);
                    },
                    child: Text(
                      '并体验完整服务',
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w400,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
