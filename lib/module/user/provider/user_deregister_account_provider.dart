import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'user_deregister_account_provider.g.dart';

@riverpod
class UserDeregisterAccount extends _$UserDeregisterAccount {
  @override
  Future<void> build() async {
    return Future.delayed(Duration(milliseconds: 300));
  }

  Future<void> deregisterAccount() async {
    final user = ref.watch(userProvider).value!;
    String currentUid = user.uid ?? "";
    List<String> list = SpUtil().getStringList("spDeregisterAccount") ?? [];
    list.add(currentUid);
    await SpUtil().setStringList("spDeregisterAccount", list);
  }

  Future<bool> isDeregisterAccount(String uid) async {
    List<String> list = SpUtil().getStringList("spDeregisterAccount") ?? [];
    return list.contains(uid);
  }
}
