import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 用户举报类型列表
final userReportTypeListProvider = FutureProvider.autoDispose<List<String>>((ref) async {
  return [
    "广告垃圾信息",
    "违反法律法规的内容",
    "其他",
  ];
});

/// 用户举报类型选择内容
final userReportSelectIndexStateProvider = StateProvider.autoDispose<String>((ref) => "");

void userReportTextEditControllerListener(ref, controller) {
  debugPrint("userReportTextEditControllerListener: ${controller.text}");
  ref.read(userReportTextEditControllerProvider.notifier).state.text = controller.text;
}

/// 用户自己输入的举报内容
final userReportTextEditControllerProvider = StateProvider.autoDispose<TextEditingController>((ref) {
  final controller = TextEditingController();
  ref.onDispose(() {
    controller.dispose();
  });

  return controller;
});

/// 用户输入的联系方式
final userReportContactEditControllerProvider = StateProvider.autoDispose<TextEditingController>((ref) => TextEditingController());

/// 上报接口
final userReportApiProvider = FutureProvider.autoDispose<bool>((ref) async {
  final content = ref.read(userReportTextEditControllerProvider).text;
  final phone = ref.read(userReportContactEditControllerProvider).text;
  final type = ref.read(userReportSelectIndexStateProvider);
  Toast.showLoading();
  try {
    final res = await ApiClient.post("/api/cqapp/front/user/feedback", {
      "feedback": "$type&$content&$phone",
    });
    Toast.closeLoading();
    return res.code == 0;
  } catch (e) {
    Toast.closeLoading();
    return false;
  }
});
