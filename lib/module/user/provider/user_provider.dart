import 'package:changqing_health_app/module/user/model/user_model.dart';
import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:changqing_health_app/service/http/response_model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_provider.g.dart';

@Riverpod(keepAlive: true)
class User extends _$User {
  @override
  Future<UserModel> build() async {
    ResponseModel resModel = await ApiClient.get("/api/cqapp/front/user/profile");
    UserModel user = UserModel.fromJson(resModel.data);
    return user;
  }
}
