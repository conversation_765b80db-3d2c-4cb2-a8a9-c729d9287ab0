import 'package:changqing_health_app/module/user/page/member_interest_page.dart';
import 'package:changqing_health_app/module/user/page/reference_link_article_page.dart';
import 'package:changqing_health_app/module/user/page/reference_link_page.dart';
import 'package:changqing_health_app/module/user/page/user_deregister_account_page.dart';
import 'package:changqing_health_app/module/user/page/user_page.dart';
import 'package:changqing_health_app/module/user/page/user_report_page.dart';
import 'package:changqing_health_app/module/user/page/youzan_mall_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';

part 'user_route.g.dart';

/// 用户路由
class UserBranchSellData extends StatefulShellBranchData {
  const UserBranchSellData();
}

class UserRoute extends GoRouteData {
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const UserPage();
  }
}

@TypedGoRoute<UserReportRoute>(path: '/user_report')
class UserReportRoute extends GoRouteData {
  const UserReportRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: UserReportPage());
  }
}

@TypedGoRoute<UserDeregisterAccountRoute>(path: '/user_deregister_account')
class UserDeregisterAccountRoute extends GoRouteData {
  const UserDeregisterAccountRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: UserDeregisterAccountPage());
  }
}

@TypedGoRoute<ReferenceLinkRoute>(path: '/reference_link')
class ReferenceLinkRoute extends GoRouteData {
  const ReferenceLinkRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: ReferenceLinkPage());
  }
}

@TypedGoRoute<ReferenceLinkArticleRoute>(path: '/reference_link_article')
class ReferenceLinkArticleRoute extends GoRouteData {
  const ReferenceLinkArticleRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: ReferenceLinkArticlePage());
  }
}

@TypedGoRoute<MemberInterestRoute>(path: '/member_interest')
class MemberInterestRoute extends GoRouteData {
  const MemberInterestRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: MemberInterestPage());
  }
}

/// 健康商城路由
@TypedGoRoute<YouzanMallRoute>(path: '/youzan_mall')
class YouzanMallRoute extends GoRouteData {
  // 商城url
  final String url;
  // 页面标题
  final String? title;
  const YouzanMallRoute({required this.url, this.title});

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return CupertinoPage<void>(child: YouzanMallPage(url: url, title: title));
  }
}
