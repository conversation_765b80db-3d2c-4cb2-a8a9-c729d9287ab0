import 'package:freezed_annotation/freezed_annotation.dart';
part 'user_model.freezed.dart';
part 'user_model.g.dart';

/// 用户模型
@freezed
class UserModel with _$UserModel {
  const UserModel._();
  const factory UserModel({
    // 用户id
    String? uid,
    // 用户名称
    String? name,
    // 用户头像
    String? avatar,
    // 有赞HUJ店铺地址
    String? yzHujWebUrl,
    // 食养会员（陪伴营）
    CompanionUserInfo? companionUserInfo,
    // 食养方案（修复营）
    NutritionPhase? nutritionPhase,
  }) = _UserModel;

  /// 非陪伴营会员
  bool get isNotCompanionVip => companionUserInfo?.state == 1;

  /// 陪伴营会员
  bool get isCompanionVip => companionUserInfo?.state == 2;

  /// 陪伴营会员到期
  bool get isCompanionVipExpired => companionUserInfo?.state == 3;

  /// 是否是修复营会员（购买了修复营课程）
  bool get isNutritionVip => nutritionPhase != null;

  /// 既不是陪伴营会员也没有购买修复营课程
  bool get isGuest => companionUserInfo == null && nutritionPhase == null;

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);
}

/// 会员模型
@freezed
class CompanionUserInfo with _$CompanionUserInfo {
  const factory CompanionUserInfo({
    // 会员状态 1-未购买 2-生效中 3-已到期
    int? state,
    // 会员到期时间
    String? endTime,
    // 会员小程序路径
    String? miniPath,
    // 会员小程序ghid
    String? miniGhid,
    // 会员权益图片地址
    String? rightUrl,
  }) = _CompanionUserInfo;

  factory CompanionUserInfo.fromJson(Map<String, dynamic> json) => _$CompanionUserInfoFromJson(json);
}

/// 营养阶段模型
@freezed
class NutritionPhase with _$NutritionPhase {
  const factory NutritionPhase({
    // 老师名称
    String? teacherName,
    // 老师头像
    String? teacherAvatar,
    // 当前阶段
    int? currentPhase,
    // 当前阶段名称
    String? currentPhaseName,
    // 当前天数
    int? currentDay,
    // 当前周数
    int? currentWeek,
    // 方案名称
    String? templateName,
    // 跳转小程序方案路径
    String? miniTemplatePath,
    // 跳转小程序营养师主页路径
    String? miniTeacherPath,
    // 小程序ghid
    String? miniGhid,
  }) = _NutritionPhase;

  factory NutritionPhase.fromJson(Map<String, dynamic> json) => _$NutritionPhaseFromJson(json);
}
