
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'agreement_provider.g.dart';

@riverpod
class AgreementProvider  extends _$AgreementProvider {
  @override
  bool build() => SpUtil().get(spPrivacyAgreement) ?? false;

  /// 切换同意状态
  void toggleAgreement() {
    state = !state;
    SpUtil().set(spPrivacyAgreement, state);
  }
}
