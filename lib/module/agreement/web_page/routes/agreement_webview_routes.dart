
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/webview/page/common_web_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
part 'agreement_webview_routes.g.dart';


@TypedGoRoute<AgreementWebviewRoute>(path: '/agreement_webview')
class AgreementWebviewRoute extends GoRouteData {
  const AgreementWebviewRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: CommonWebPage(url: userAgreementUrl, title: '用户服务协议'));
  }
}
@TypedGoRoute<PrivacyPolicyWebviewRoute>(path: '/privacy_policy_webview')
class PrivacyPolicyWebviewRoute extends GoRouteData {
  const PrivacyPolicyWebviewRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: CommonWebPage(url: privacyAgreementUrl, title: '隐私政策'));
  }
}