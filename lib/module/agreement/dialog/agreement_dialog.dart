import 'package:changqing_health_app/app/init.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:go_router/go_router.dart';
import 'package:changqing_health_app/module/agreement/web_page/routes/agreement_webview_routes.dart';

/// 用户协议弹窗
class AgreementDialog extends StatelessWidget {
  final Function(bool isAgree)? onAgree;
  const AgreementDialog({super.key, this.onAgree});

  static showDialog(BuildContext context, {Function(bool isAgree)? onAgree}) {
    final isFirstInstallAgreement = SpUtil().get<bool>(spFirstInstallAgreement) ?? false;
    if (isFirstInstallAgreement) {
      return;
    }
    showModalBottomSheet(
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      enableDrag: false,
      context: context,
      isDismissible: false,
      builder: (context) {
        return AgreementDialog(onAgree: onAgree);
      },
    );
  }

// 跳转用户协议页面
  void _jumpToAgreementPage(BuildContext context) {
    context.push(AgreementWebviewRoute().location);
  }

// 跳转隐私政策页面
  void _jumpToPrivacyPolicyPage(BuildContext context) {
    context.push(PrivacyPolicyWebviewRoute().location);
  }

// 同意并继续
  void _agreeAndContinue(BuildContext context) {
    SpUtil().set(spFirstInstallAgreement, true);
    Navigator.pop(context);
    onAgree?.call(true);
  }

// 不同意
  void _disagree() {
    onAgree?.call(false);
    Toast.show('不同意则无法使用本App');
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(height: 24),
            const Text(
              '温馨提示',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            10.gap,
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    RichText(
                      text: TextSpan(
                        style: const TextStyle(fontSize: 15, color: Colors.black),
                        children: [
                          const TextSpan(text: '欢迎使用长轻营养⾷疗。在使用长轻营养⾷疗前，请认真阅读'),
                          TextSpan(
                            text: '《用户服务协议》',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                // 处理用户协议点击事件
                                _jumpToAgreementPage(context);
                              },
                          ),
                          const TextSpan(text: '、'),
                          TextSpan(
                            text: '《隐私政策》',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                // 处理隐私政策点击事件
                                _jumpToPrivacyPolicyPage(context);
                              },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      '我们承诺会采取业界先进的安全措施保护你的信息安全。',
                      style: TextStyle(fontSize: 15),
                    ),
                    const SizedBox(height: 16),
                    // const Text(
                    //   '免责声明：本应用中的饮食计划和建议基于科学研究和一般营养学知识，不能替代专业医生、营养师或健康专家的建议，如您身体不适，请及时就医或咨询专业医疗人员。',
                    //   style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Colors.red),
                    // ),
                    // const SizedBox(height: 16),
                    const Text(
                      '为实现更丰富的服务功能，基于你的授权，我们可能获取以下权限和信息：',
                      style: TextStyle(fontSize: 15),
                    ),
                    _buildPermissionItem('常用设备信息：为检查你的网络运营环境，保障你的账号与健康信息安全性'),
                    // _buildPermissionItem('相机/相册信息：为你提供上传图片的功能服务'),
                    const SizedBox(height: 16),
                    const Text(
                      '请知悉，你同意本弹窗内容相应设备权限并不会默认开启，我们会在你使用到相应业务功能时，另行弹窗征得你的同意后开启',
                      style: TextStyle(fontSize: 15),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: _disagree,
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey.shade300),
                        ),
                      ),
                      child: const Text(
                        '不同意',
                        style: TextStyle(
                          color: Colors.black54,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextButton(
                      onPressed: () => _agreeAndContinue(context),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.blue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '同意并继续',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SafeArea(child: SizedBox(height: 0)),
            // 10.gap,
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 8),
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 15),
            ),
          ),
        ],
      ),
    );
  }
}
