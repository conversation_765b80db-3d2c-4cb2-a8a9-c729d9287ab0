import 'package:changqing_health_app/module/community/page/community_page.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// 首页路由
class HomeBranchSellData extends StatefulShellBranchData {
  const HomeBranchSellData();
}

class HomeRoute extends GoRouteData {

  @override
  Widget build(BuildContext context, GoRouterState state) {
    // return const HomePage();
    return const CommunityPage();
  }


}
