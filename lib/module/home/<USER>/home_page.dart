// import 'package:changqing_health_app/config/env_config.dart';
// import 'package:changqing_health_app/constant/constant.dart';
// import 'package:changqing_health_app/extension/context_ext.dart';
// import 'package:changqing_health_app/extension/num_ext.dart';
// import 'package:changqing_health_app/module/login/provider/login_provider.dart';
// import 'package:changqing_health_app/module/login/route/login_route.dart';
// import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
// import 'package:changqing_health_app/module/video/route/video_route.dart';
// import 'package:changqing_health_app/util/sp_util.dart';
// import 'package:cq_flutter_ai_chat/module/assistant/init.dart';
// import 'package:cq_flutter_ai_chat/module/assistant/model/assistant_config.dart';
// import 'package:cq_flutter_ai_chat/module/chat/provider/chat_provider.dart';
// import 'package:cq_flutter_ai_chat/module/chat/provider/input_provider.dart';
// import 'package:cq_flutter_ai_chat/module/chat/provider/session_provider.dart';
// import 'package:cq_flutter_ai_chat/module/chat/widget/chat_box.dart';
// import 'package:cq_flutter_ai_chat/widget/package_assets_image.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:go_router/go_router.dart';
// import 'package:url_launcher/url_launcher.dart';
//
// /// 首页
// class HomePage extends ConsumerStatefulWidget {
//   const HomePage({super.key});
//
//   @override
//   ConsumerState<ConsumerStatefulWidget> createState() => _State();
// }
//
// class _State extends ConsumerState<HomePage> {
//   @override
//   void initState() {
//     super.initState();
//     if (ref.read(loginProvider)) {
//       _initCQAssistant();
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final isLogin = ref.watch(loginProvider);
//     ref.listen(loginProvider, (previous, next) {
//       if (previous == false && next == true) {
//         _initCQAssistant();
//       }
//     });
//     return Scaffold(
//       appBar: PreferredSize(
//         preferredSize: Size(0, 0),
//         child: Container(
//           color: Colors.transparent,
//         ),
//       ),
//       body: isLogin ? _buildChatBox() : _buildUnlogin(),
//     );
//   }
//
//   /// 构建聊天界面
//   Widget _buildChatBox() {
//     return Column(
//       children: [
//         _buildHeader(),
//         Expanded(
//           child: ChatBox(
//             onVideoTap: (url) async {
//               VideoRoute(videoUrl: url).push(context);
//             },
//             onLinkTap: _onLinkTap,
//           ),
//         ),
//       ],
//     );
//   }
//
//   /// 构建头部
//   Widget _buildHeader() {
//     return Builder(builder: (context) {
//       return Container(
//         height: kToolbarHeight,
//         color: Colors.transparent,
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             IconButton(
//               padding: EdgeInsets.zero,
//               onPressed: _openSessionList,
//               icon: const Icon(Icons.menu_open_rounded),
//               iconSize: 50.w,
//             ),
//             Text(
//               '对话',
//               style: TextStyle(
//                 fontSize: 32.sp,
//                 fontWeight: FontWeight.w500,
//               ),
//             ),
//             IconButton(
//               padding: EdgeInsets.zero,
//               onPressed: _createNewChat,
//               icon: Image.asset(
//                 'assets/images/new-message.png',
//                 width: 38.w,
//                 // height: 40.w,
//               ),
//             ),
//           ],
//         ),
//       );
//     });
//   }
//
//   /// 构建未登录界面
//   Widget _buildUnlogin() {
//     return SizedBox(
//       width: context.sw,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.center,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Row(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               PackageAssetsImage(
//                 'assets/images/chat/logo.webp',
//                 width: 80.w,
//                 height: 80.w,
//                 fit: BoxFit.cover,
//               ),
//               24.gap,
//               Text(
//                 '我是小轻，很高兴见到你!',
//                 style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.w600),
//               ),
//             ],
//           ),
//           16.gap,
//           Padding(
//             padding: EdgeInsets.symmetric(horizontal: 80.w),
//             child: Text(
//               '在这里，我是你的专属健康顾问，时刻解决你的健康问题',
//               textAlign: TextAlign.center,
//               style: TextStyle(
//                 letterSpacing: 1,
//                 fontSize: 26.w,
//                 height: 1.5,
//                 color: Color(0xFF666666),
//               ),
//             ),
//           ),
//           16.gap,
//           CupertinoButton(
//             padding: EdgeInsets.zero,
//             onPressed: () {
//               context.push(ref.read(getLoginRouteProvider));
//             },
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 80.w),
//               child: Text(
//                 '点击登录以使用 AI 助手功能',
//                 textAlign: TextAlign.center,
//                 style: TextStyle(
//                   letterSpacing: 1,
//                   fontSize: 26.w,
//                   height: 1.5,
//                   color: CupertinoColors.systemBlue,
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
//
//   /// 打开对话列表
//   void _openSessionList() {
//     ref.read(inputStateProvider.notifier).unfocus();
//     HapticFeedback.mediumImpact();
//     context.findRootAncestorStateOfType<ScaffoldState>()?.openDrawer();
//     ref.refresh(sessionProvider);
//   }
//
//   /// 创建新对话
//   void _createNewChat() {
//     ref.read(chatProvider.notifier).makeNewChat(context: context);
//   }
//
//   /// 初始化CQAssistant
//   void _initCQAssistant() {
//     CQAssistant.init(
//       config: AssistantConfig(
//         baseUrl: EnvConfig().appBaseUrl!,
//         usePopupInput: true,
//         uid: SpUtil().get(spAppUid) ?? '',
//         token: SpUtil().get(spAppToken) ?? '',
//         proxy: SpUtil().get(spAppNetworkProxy),
//         // enableFileInput: true, // 是否启用文件输入 ref.read(appVersionProviderProvider.notifier).isReview
//         enableFileInput: !ref.read(appVersionProviderProvider.notifier).isReview,
//         showBottomInputWidget: false,
//       ),
//     );
//   }
//
//   Future<void> _onLinkTap(String url) async {
//     final uri = Uri.parse(url);
//     if (uri.host == "www.cqslim.com" || uri.host == "mobile.cqslim.com") {
//       // 给 uri 添加参数 pageFrom=app
//       final newUri = _addQueryParameters(uri, {'pageFrom': 'app'});
//       launchUrl(newUri, mode: LaunchMode.inAppBrowserView);
//     } else if (uri.host.contains("www.example.com")) {
//       launchUrl(Uri.parse(cqServiceUrl), mode: LaunchMode.inAppBrowserView);
//     } else {
//       launchUrl(Uri.parse(url), mode: LaunchMode.inAppBrowserView);
//     }
//   }
//
//   Uri _addQueryParameters(Uri originalUri, Map<String, String> newParams) {
//     // 合并原有参数和新参数
//     final updatedQueryParams = Map<String, String>.from(originalUri.queryParameters)..addAll(newParams);
//
//     // 返回一个新的 URI
//     return originalUri.replace(queryParameters: updatedQueryParams);
//   }
// }
