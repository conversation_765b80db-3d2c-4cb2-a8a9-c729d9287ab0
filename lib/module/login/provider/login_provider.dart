import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/login/api/login_api.dart';
import 'package:changqing_health_app/module/login/model/login_response_model.dart';
import 'package:changqing_health_app/module/navigator/provider/bottom_bar_provider.dart';
import 'package:changqing_health_app/module/user/provider/user_deregister_account_provider.dart';
import 'package:changqing_health_app/module/user/provider/user_provider.dart';
import 'package:changqing_health_app/util/sentry_util.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:changqing_health_app/util/youzan_util.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
part 'login_provider.g.dart';

/// 记录是否在发送验证码中
final isSendingVerifyCodeProvider = StateProvider.autoDispose<bool>((ref) => false);

@riverpod
class Login extends _$Login {
  @override
  bool build() => SpUtil().get(spAppToken) != null;

  /// 发送验证码
  Future<bool> sendVerifyCode(String phone) async {
    ref.read(isSendingVerifyCodeProvider.notifier).state = true;
    try {
      final resModel = await LoginApi.sendVerifyCode(phone);
      if (resModel.code == 0) {
        Toast.show('验证码发送成功');
        ref.read(isSendingVerifyCodeProvider.notifier).state = false;
        return true;
      } else {
        Toast.show(resModel.msg);
        ref.read(isSendingVerifyCodeProvider.notifier).state = false;
        return false;
      }
    } catch (e) {
      Toast.show('验证码发送失败');
      ref.read(isSendingVerifyCodeProvider.notifier).state = false;
      return false;
    }
  }

  /// 验证码登录
  Future<void> loginByCode(String phone, String verifyCode) async {
    try {
      final loginResponseModel = await LoginApi.loginByCode(phone, verifyCode);
      await _loginSuccess(loginResponseModel);
    } catch (e) {
      Toast.show('登录失败');
    }
  }

  /// 微信登录
  Future<void> loginByWechat(String code) async {
    Toast.showLoading();
    try {
      final loginResponseModel = await LoginApi.loginByWechat(code);
      await _loginSuccess(loginResponseModel);
    } catch (e) {
      Toast.show('微信登录失败');
    } finally {
      Toast.closeLoading();
    }
  }

  Future<void> _loginSuccess(LoginResponseModel loginResponseModel) async {
    try {
      /// 本地校验是否已注销账户
      final isDeregisterAccount = await ref.read(userDeregisterAccountProvider.notifier).isDeregisterAccount(loginResponseModel.uid);
      if (isDeregisterAccount) {
        Toast.show('该账号已注销');
        return;
      }
      String token = loginResponseModel.token;
      String? jwt = loginResponseModel.jwt;
      int? mid = loginResponseModel.mid;

      await SpUtil().set(spAppToken, token);
      await SpUtil().set(spAppUid, loginResponseModel.uid);
      if (mid != null) {
        await SpUtil().set(spAppMid, mid.toString());
      }
      if (jwt != null) {
        await SpUtil().set(spAppJwt, jwt);
      }
      ref.refresh(userProvider);
      // 登录有赞
      YouzanUtil.login(userId: SpUtil().get(spAppMid));
      ref.read(bottomBarProvider.notifier).toggle(homeBarModel);
    } catch (e) {
      SentryUtil.captureException(e, stackTrace: StackTrace.current);
    } finally {
      state = true;
    }
  }

  /// 退出登录
  Future<void> logout() async {
    // ref.read(selectedSessionProvider.notifier).clearSession();
    await SpUtil().remove(spAppToken);
    await SpUtil().remove(spAppUid);
    await SpUtil().remove(spAppMid);
    await SpUtil().remove(spAppJwt);
    await YouzanUtil.logout();
    state = false;
    // Toast.show('退出登录成功');
  }
}
