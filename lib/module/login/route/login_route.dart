import 'dart:io';

import 'package:changqing_health_app/module/login/page/login_phone_page.dart';
import 'package:changqing_health_app/module/login/page/login_wechat_page.dart';
import 'package:changqing_health_app/module/other/check_version/app_version_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

part 'login_route.g.dart';

/// 获取登录路由
/// 如果是安卓，则一直都是微信登录
/// 如果是iOS平台，则需要获取是否在审核中
/// 如果在审核中则手机号登录
/// 否则微信登录

final getLoginRouteProvider = Provider((ref) {
  if (Platform.isIOS) {
    if (ref.read(appVersionProviderProvider.notifier).isReview) {
      return const LoginPhoneRoute().location;
    } else {
      return const LoginWechatRoute().location;
    }
  } else {
    return const LoginWechatRoute().location;
  }
});

/// 登录相关的路由
@TypedGoRoute<LoginPhoneRoute>(path: '/login/phone')
class LoginPhoneRoute extends GoRouteData {
  const LoginPhoneRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: LoginPhonePage(), fullscreenDialog: true);
  }
}

/// 微信登录
@TypedGoRoute<LoginWechatRoute>(path: '/login/wechat')
class LoginWechatRoute extends GoRouteData {
  const LoginWechatRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const CupertinoPage<void>(child: LoginWechatPage(), fullscreenDialog: true);
  }
}
