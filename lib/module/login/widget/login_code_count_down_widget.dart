import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';

import 'package:flutter_screenutil/flutter_screenutil.dart';

class LoginCodeCountDownController extends ChangeNotifier {
  final String title;
  final int count;

  Timer? _timer;
  int _count = 0;
  bool _isCounting = false;

  LoginCodeCountDownController({
    required this.title,
    this.count = 60,
  });

  void startCountDown() {
    if (_timer != null) return;
    _count = count;
    _isCounting = true;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _count--;
      if (_count == 0) {
        reset();
      }
      notifyListeners();
    });
  }

  void reset() {
    _isCounting = false;
    _count = count;
    _timer?.cancel();
    _timer = null;
  }
}


class LoginCodeCountDownWidget extends ConsumerStatefulWidget {
  final String? Function() getPhone;
  const LoginCodeCountDownWidget({super.key, required this.getPhone});

  @override
  ConsumerState<LoginCodeCountDownWidget> createState() => _LoginCodeCountDownWidgetState();
}

class _LoginCodeCountDownWidgetState extends ConsumerState<LoginCodeCountDownWidget> {
  final LoginCodeCountDownController controller = LoginCodeCountDownController(title: '发送验证码', count: 60);
  @override
  void initState() {
    super.initState();
    controller.addListener(_setState);
  }

  void _setState() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSendingVerifyCode = ref.watch(isSendingVerifyCodeProvider);
    return AbsorbPointer(
      absorbing: isSendingVerifyCode || controller._isCounting,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () async {
            if (widget.getPhone() == null) return;
            HapticFeedback.mediumImpact();
            bool isSuccess = await ref.read(loginProvider.notifier).sendVerifyCode(widget.getPhone()!);
            if (isSuccess) {
              controller.startCountDown();
            }
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: 110,
            height: 50,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xffcecece), width: 1.2),
            ),
            alignment: Alignment.center,
            child: isSendingVerifyCode
                ? CupertinoActivityIndicator(color: Colors.black)
                : Text(
                    _getTitle,
                    style: TextStyle(fontSize: 30.sp, color: Colors.black, fontWeight: FontWeight.normal),
                  ),
          ),
        ),
      ),
    );
  }

  String get _getTitle {
    if (controller._isCounting) {
      return "${controller._count}s";
    } else {
      return controller.title;
    }
  }

  @override
  void dispose() {
    controller.removeListener(_setState);
    super.dispose();
  }
}
