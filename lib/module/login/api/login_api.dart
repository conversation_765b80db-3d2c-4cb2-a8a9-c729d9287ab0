import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/login/model/login_response_model.dart';
import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:changqing_health_app/service/http/response_model.dart';
import 'package:changqing_health_app/service/http/sign/aes_helper.dart';
import 'package:changqing_health_app/util/sp_util.dart';

class LoginApi {
  LoginApi._();

  /// 发送验证码
  static Future<ResponseModel> sendVerifyCode(String phone) async {
    try {
      final encryptedPhone = AESHelper.encryptText(phone);
      ResponseModel resModel = await ApiClient.post("/api/cqapp/front/user/mobile/code", {"phoneNumber": encryptedPhone});
      return resModel;
    } catch (e) {
      throw Exception(e);
    }
  }

  /// 验证码登录
  static Future<LoginResponseModel> loginByCode(String phone, String verifyCode) async {
    try {
      final encryptedPhone = AESHelper.encryptText(phone);
      ResponseModel resModel = await ApiClient.post("/api/cqapp/front/user/mobile/login", {"phoneNumber": encryptedPhone, "code": verifyCode});
      return LoginResponseModel.fromJson(resModel.data);
    } catch (e) {
      throw Exception(e);
    }
  }

  /// 微信登录
  static Future<LoginResponseModel> loginByWechat(String code) async {
    try {
      ResponseModel resModel = await ApiClient.post("/api/cqapp/front/user/weixin/login", {"code": code});
      return LoginResponseModel.fromJson(resModel.data);
    } catch (e) {
      throw Exception(e);
    }
  } 

    /// 刷新token
  static Future<bool> refreshToken() async {
    try {
      final mid = SpUtil().get(spAppMid);
      ResponseModel resModel = await ApiClient.get("/api/cqapp/front/user/byMid", param: {"mid": mid});
      LoginResponseModel model = LoginResponseModel.fromJson(resModel.data);
      await SpUtil().set(spAppToken, model.token);
      await SpUtil().set(spAppJwt, model.jwt);
      return true;
    } catch (e) {
      return false;
    }
  } 
}
