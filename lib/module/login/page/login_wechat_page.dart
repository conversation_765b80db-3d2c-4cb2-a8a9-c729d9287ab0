import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/agreement/provider/agreement_provider.dart';
import 'package:changqing_health_app/module/home/<USER>/home_route.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/navigator/provider/bottom_bar_provider.dart';
import 'package:changqing_health_app/module/navigator/route/navigator_route.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluwx/fluwx.dart';
import 'package:go_router/go_router.dart';
import 'package:changqing_health_app/module/agreement/web_page/routes/agreement_webview_routes.dart' as agreement_routes;

/// 微信登录
class LoginWechatPage extends ConsumerStatefulWidget {
  const LoginWechatPage({super.key});

  @override
  ConsumerState<LoginWechatPage> createState() => _LoginWechatPageState();
}

class _LoginWechatPageState extends ConsumerState<LoginWechatPage> {
  final _fluwx = Fluwx();

  @override
  void initState() {
    super.initState();
    _fluwx.addSubscriber(_onWechatLogin);
  }

  @override
  void dispose() {
    _fluwx.removeSubscriber(_onWechatLogin);
    super.dispose();
  }

  void _onWechatLogin(value) async {
    if (value is WeChatAuthResponse) {
      WeChatAuthResponse response = value;
      debugPrint('微信登录 errCode = ${response.errCode}');
      debugPrint('微信登录 code = ${response.code}');
      if (response.errCode == 0) {
        await ref.read(loginProvider.notifier).loginByWechat(response.code!);
        if (ref.read(loginProvider)) {
          if (mounted) {
            HomeRoute().go(context);
            try {
              ref.read(bottomBarProvider.notifier).toggle(homeBarModel);
            }catch(_){}
            // if (context.canPop()) {
            //   debugPrint('-------isLogin 111');
            //   context.pop();
            // } else {
            //   debugPrint('-------isLogin 222');
            //   HomeRoute().go(context);
            // }
          }
        }
      } else {
        // 登录失败
        Toast.show('微信登录失败');
      }
    }
  }

  void _loginWechat() async {
    _fluwx.authBy(which: NormalAuth(scope: "snsapi_userinfo", state: "cqappurltype")).then((value) {});
  }

  void _onPop() {
    if (context.canPop()) {
      context.pop();
    } else {
      HomeRoute().go(context);
      try {
        ref.read(bottomBarProvider.notifier).toggle(homeBarModel);
      }catch(_){}
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        _onPop();
      },
      child: Scaffold(
        body: Stack(
          children: [
            Container(width: double.maxFinite, height: double.maxFinite, color: Colors.white),
            Positioned(left: 0, right: 0, top: 0, child: Image.asset('assets/images/login/back_icon.png', height: 460.w)),
            Positioned(
              left: 0,
              right: 0,
              top: 218.w + kToolbarHeight,
              child: Column(
                children: [
                  Image.asset('assets/images/login/logo_icon.png', height: 134.w),
                  SizedBox(height: 48.w),
                  Text('欢迎来到长轻营养⾷疗', style: TextStyle(color: Color(0xFF333333), fontSize: 54.sp, fontWeight: FontWeight.w700)),
                  SizedBox(height: 12.w),
                  Text('让身体恢复健康 让生命重获美好', style: TextStyle(color: Color(0xFF333333), fontSize: 28.sp, fontWeight: FontWeight.w700)),
                  SizedBox(height: 240.w),
                  _buildBtnLogin(),
                  SizedBox(height: 24.w),
                  _buildBtnAgreement(),
                ],
              ),
            ),
            Positioned(
              top: MediaQuery.of(context).padding.top,
              left: 24.w,
              width: kToolbarHeight,
              height: kToolbarHeight,
              child: GestureDetector(
                onTap: () {
                  _onPop();
                },
                child: Container(
                  color: Colors.transparent,
                  alignment: Alignment.centerLeft,
                  child: Icon(Icons.arrow_back, color: Color(0xff34B001)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBtnLogin() {
    final borderRadius = BorderRadius.circular(12);
    return Material(
      color: const Color(0xff34B001),
      borderRadius: borderRadius,
      child: SizedBox(
        width: 630.w,
        height: 100.w,
        child: InkWell(
          onTap: () async {
            if (!ref.read(agreementProviderProvider)) {
              Toast.show('请先同意用户服务协议和隐私政策');
              return;
            }
            HapticFeedback.mediumImpact();
            _loginWechat();
          },
          borderRadius: borderRadius,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 150),
            width: 300,
            height: 100,
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '登录',
                  style: TextStyle(fontSize: 32.sp, color: Colors.white, fontWeight: FontWeight.w700),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 同意协议按钮
  Widget _buildBtnAgreement() {
    final isAgreement = ref.watch(agreementProviderProvider);
    return Container(
      width: 600.w,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              ref.read(agreementProviderProvider.notifier).toggleAgreement();
            },
            child: Container(
              width: 55.w,
              height: 55.w,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: Image.asset('assets/images/login/${isAgreement ? 'agreement_icon_select.png' : 'agreement_icon_normal.png'}', width: 32.w),
            ),
          ),
          RichText(
            text: TextSpan(
              style: TextStyle(fontSize: 28.sp, height: 1.3),
              children: [
                TextSpan(text: '已阅读并同意 ', style: TextStyle(color: const Color(0xFF999999))),
                TextSpan(
                    text: '用户服务协议',
                    style: TextStyle(color: const Color(0xFF4D7FEC)),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        context.push(agreement_routes.AgreementWebviewRoute().location);
                      }),
                TextSpan(text: ' 和 ', style: TextStyle(color: const Color(0xff8b8b8b))),
                TextSpan(
                  text: '隐私政策',
                  style: TextStyle(color: const Color(0xFF4D7FEC)),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      context.push(agreement_routes.PrivacyPolicyWebviewRoute().location);
                    },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
