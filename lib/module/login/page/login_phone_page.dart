import 'package:cached_network_image/cached_network_image.dart';
import 'package:changqing_health_app/extension/num_ext.dart';
import 'package:changqing_health_app/module/agreement/provider/agreement_provider.dart';
import 'package:changqing_health_app/module/home/<USER>/home_route.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/module/login/widget/login_code_count_down_widget.dart';
import 'package:changqing_health_app/module/navigator/provider/bottom_bar_provider.dart';
import 'package:changqing_health_app/module/navigator/route/navigator_route.dart';
import 'package:changqing_health_app/module/user/provider/user_deregister_account_provider.dart';
import 'package:changqing_health_app/util/toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:changqing_health_app/module/agreement/web_page/routes/agreement_webview_routes.dart' as agreement_routes;

/// 手机号验证码登录
class LoginPhonePage extends ConsumerStatefulWidget {
  const LoginPhonePage({super.key});

  @override
  ConsumerState<LoginPhonePage> createState() => _LoginPhonePageState();
}

class _LoginPhonePageState extends ConsumerState<LoginPhonePage> {
  final phoneController = TextEditingController();
  final verifyCodeController = TextEditingController();

  bool isPhoneFocused = false;
  bool isVerifyCodeFocused = false;

  final phoneFocusNode = FocusNode();
  final verifyCodeFocusNode = FocusNode();

  bool _isLoginLoading = false;

  double get _imageHeight => 1.sw / 1500 * 1120;

  @override
  void initState() {
    super.initState();
    phoneFocusNode.addListener(() {
      setState(() {
        isPhoneFocused = phoneFocusNode.hasFocus;
      });
    });
    verifyCodeFocusNode.addListener(() {
      setState(() {
        isVerifyCodeFocused = verifyCodeFocusNode.hasFocus;
      });
    });

    phoneController.addListener(() {
      _setState();
    });
    verifyCodeController.addListener(() {
      _setState();
    });
  }

  @override
  void dispose() {
    phoneController.removeListener(_setState);
    verifyCodeController.removeListener(_setState);
    phoneController.dispose();
    verifyCodeController.dispose();
    phoneFocusNode.dispose();
    verifyCodeFocusNode.dispose();
    super.dispose();
  }

  void _setState() {
    setState(() {});
  }

  // 添加一个判断输入是否有效的方法
  bool _isInputValid() {
    final phone = phoneController.text.replaceAll(' ', '');
    final password = verifyCodeController.text;
    return phone.length == 11 && password.isNotEmpty;
  }

  bool _isPhoneValid() {
    final phone = phoneController.text.replaceAll(' ', '');
    if (phone.isEmpty) {
      Toast.show('手机号不能为空');
      return false;
    }
    if (phone.length != 11) {
      Toast.show('手机号格式不正确');
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF1FAF0), // 极浅薄荷绿，几乎白色，作为起始渐变
              Color(0xFFDFF5DF), // 柔和草绿，清新感觉
              Color(0xFFBEEFC1), // 淡嫩绿色，有生机
              Color(0xFF9EE7A3), // 稍浓一点，草绿渐现
              Color(0xFF80D68A), // 柔和草绿色，依然清淡不刺眼
            ],
          ),
        ),
        child: Stack(
          children: [
            // Positioned.fill(
            //   child: Image.asset(
            //     'assets/images/chat/bg_login.png',
            //     fit: BoxFit.cover,
            //     width: double.infinity,
            //     height: double.infinity,
            //   ),
            // ),
            Positioned(
              left: 0,
              top: 0,
              right: 0,
              height: _imageHeight,
              child: Image.asset(
                'assets/images/login_back_image.png',
                height: _imageHeight,
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              top: _imageHeight - 30.w - MediaQuery.of(context).viewInsets.bottom * 0.35,
              child: _bodyWidget(),
            ),
            Positioned(
              left: 12.w,
              top: MediaQuery.of(context).padding.top,
              child: GestureDetector(
                onTap: (){
                  context.pop();
                },
                child: Container(
                  width: 100.w,
                  height: 100.w,
                  color: Colors.transparent,
                  child: Icon(Icons.arrow_back_ios_new, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _bodyWidget() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(30.w),
      child: Container(
        color: Color(0xFFF0F0F0),
        child: Column(
          children: [
            SizedBox(height: 24.w),
            Text('登录/注册', style: TextStyle(fontSize: 60.sp, fontWeight: FontWeight.bold)),
            20.gap,
            _buildPhone(),
            20.gap,
            _buildVerifyCodeWidget(),
            40.gap,
            _buildBtnLogin(),
            20.gap,
            _buildBtnAgreement(),
          ],
        ),
      ),
    );
  }

  /// 同意协议按钮
  Widget _buildBtnAgreement() {
    final isAgreement = ref.watch(agreementProviderProvider);
    return Container(
      width: 600.w,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // check_circle_outline
          // circle
          // IconButton(

          //     onPressed: () {
          //       ref.read(agreementProviderProvider.notifier).toggleAgreement();
          //     },
          //     icon: Icon(isAgreement ? Icons.check_circle_outline : Icons.circle_outlined, color: const Color(0xff3477f0),
          //         size: 28.w)),
          GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
              ref.read(agreementProviderProvider.notifier).toggleAgreement();
            },
            child: Container(
              width: 44.w,
              height: 44.w,
              color: Colors.transparent,
              child: Icon(isAgreement ? Icons.check_circle_outline : Icons.circle_outlined, color: const Color(0xff3477f0), size: 34.w),
            ),
          ),
          RichText(
            text: TextSpan(
              style: TextStyle(fontSize: 28.sp, height: 1.3),
              children: [
                TextSpan(text: '已阅读并同意 ', style: TextStyle(color: const Color(0xff8b8b8b))),
                TextSpan(
                    text: '用户服务协议',
                    style: TextStyle(color: const Color(0xff3477f0)),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        context.push(agreement_routes.AgreementWebviewRoute().location);
                      }),
                TextSpan(text: ' 和 ', style: TextStyle(color: const Color(0xff8b8b8b))),
                TextSpan(
                  text: '隐私政策',
                  style: TextStyle(color: const Color(0xff3477f0)),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      context.push(agreement_routes.PrivacyPolicyWebviewRoute().location);
                    },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBtnLogin() {
    final borderRadius = BorderRadius.circular(12);
    final isValid = _isInputValid();
    return AbsorbPointer(
      absorbing: _isLoginLoading || !isValid,
      child: Material(
        color: isValid
            ? const Color(0xff3B82F6) // 有效输入时的蓝色
            : const Color(0xffBEBEBE),
        borderRadius: borderRadius,
        child: SizedBox(
          width: 600.w,
          child: InkWell(
            onTap: () async {
              if (!ref.read(agreementProviderProvider)) {
                Toast.show('请先同意用户服务协议和隐私政策');
                return;
              }
              HapticFeedback.mediumImpact();
              setState(() {
                _isLoginLoading = !_isLoginLoading;
              });
              await _login();
            },
            borderRadius: borderRadius,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              width: 300,
              height: 50,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _isLoginLoading
                      ? CupertinoActivityIndicator(color: Colors.white)
                      : Text(
                          '登录',
                          style: TextStyle(fontSize: 34.sp, color: Colors.white, fontWeight: FontWeight.normal),
                        )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBtnVerifyCode() {
    return AbsorbPointer(
      absorbing: _isLoginLoading,
      child: LoginCodeCountDownWidget(getPhone: () {
        if (!_isPhoneValid()) {
          return null;
        }
        // 是否同意协议
        if (!ref.read(agreementProviderProvider)) {
          Toast.show('请先同意用户服务协议和隐私政策');
          return null;
        }
        return phoneController.text.replaceAll(' ', '');
      }),
    );
  }

  Widget _buildVerifyCode() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 150),
      width: 640.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isVerifyCodeFocused ? const Color(0xff3477f0) : const Color(0xffcecece),
          width: isVerifyCodeFocused ? 2 : 1.2,
        ),
      ),
      child: Row(
        children: [
          20.gap,
          SvgPicture.asset(
            "assets/images/chat/verify_code.svg",
            width: 40.w,
            height: 40.w,
            colorFilter: const ColorFilter.mode(Color(0xff8d8d8d), BlendMode.srcIn),
          ),
          12.gap,
          Expanded(
            child: TextField(
              cursorColor: const Color(0xff3477f0),
              focusNode: verifyCodeFocusNode,
              controller: verifyCodeController,
              maxLength: 6,
              maxLines: 1,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              // 输入数字
              keyboardType: TextInputType.number,
              onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: '验证码',
                counterText: '',
                hintStyle: TextStyle(
                  color: const Color(0xffBBBBBB),
                  fontSize: 30.sp,
                  fontWeight: FontWeight.normal,
                  height: 1.0, // 添加行高设置
                ),
              ),
              style: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  /// 验证码输入框
  /// 第一次提审时先改成密码输入框
  Widget _buildVerifyCodeWidget() {
    return Container(
      width: 600.w,
      height: 120.w,
      decoration: BoxDecoration(
        color: Colors.transparent,
        // border: Border.all(color: Colors.grey),
      ),
      child: Row(
        children: [
          Expanded(child: _buildVerifyCode()),
          12.gap,
          _buildBtnVerifyCode(),
        ],
      ),
    );
  }

  Widget _buildPhone() {
    return Container(
      width: 600.w,
      height: 120.w,
      decoration: BoxDecoration(
        color: Colors.transparent,
      ),
      alignment: Alignment.center,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        width: 600.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(24.w),
          border: Border.all(
            color: isPhoneFocused ? const Color(0xff3477f0) : const Color(0xffcecece),
            width: isPhoneFocused ? 1.2 : 0.8,
          ),
        ),
        child: Row(
          children: [
            12.gap,
            SvgPicture.asset("assets/images/chat/phone.svg",
                width: 44.w,
                height: 44.w,
                colorFilter: const ColorFilter.mode(
                  Color(0xff8d8d8d),
                  BlendMode.srcIn,
                )),
            12.gap,
            Text('+86', style: TextStyle(fontSize: 30.sp, color: Colors.black, fontWeight: FontWeight.bold)),
            24.gap,
            Expanded(
              child: TextField(
                cursorColor: const Color(0xff3477f0),
                controller: phoneController,
                focusNode: phoneFocusNode,
                keyboardType: TextInputType.number,
                maxLength: 13,
                maxLines: 1,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  _PhoneNumberFormatter(),
                ],
                onTapOutside: (_) => FocusManager.instance.primaryFocus?.unfocus(),
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: '手机号',
                  counterText: '',
                  hintStyle: TextStyle(
                    color: const Color(0xffBBBBBB),
                    fontSize: 30.sp,
                    fontWeight: FontWeight.normal,
                    height: 1.0,
                  ),
                ),
                style: TextStyle(fontSize: 30.sp, color: Colors.black, fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _login() async {
    try {
      if (!_isPhoneValid()) {
        return;
      }
      final phone = phoneController.text.replaceAll(' ', '');
      final verifyCode = verifyCodeController.text;
      if (verifyCode.isEmpty) {
        Toast.show('验证码不能为空');
        return;
      }
      if (verifyCode.length != 6) {
        Toast.show('验证码格式不正确');
        return;
      }

      await ref.read(loginProvider.notifier).loginByCode(phone, verifyCode);

      if (ref.read(loginProvider)) {
        if (mounted) {
          HomeRoute().go(context);
          try {
            ref.read(bottomBarProvider.notifier).toggle(homeBarModel);
          }catch(_){}
        }
      } else {
        verifyCodeController.clear();
      }
    } catch (e) {
      Toast.show('登录失败，请重试');
    } finally {
      setState(() {
        _isLoginLoading = false;
      });
    }
  }
}

class _PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // 只保留数字
    final text = newValue.text.replaceAll(RegExp(r'\D'), '');

    var newText = '';
    for (var i = 0; i < text.length; i++) {
      if (i == 3 || i == 7) {
        newText += ' '; // 在第3位和第7位后添加空格
      }
      newText += text[i];
    }

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}
