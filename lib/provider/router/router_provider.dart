import 'package:bot_toast/bot_toast.dart';
import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/module/login/provider/login_provider.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:changqing_health_app/module/root/route/root_route.dart' as root_routes;
import 'package:changqing_health_app/module/navigator/route/navigator_route.dart' as navigator_routes;
import 'package:changqing_health_app/module/video/route/video_route.dart' as video_routes;
import 'package:changqing_health_app/module/community/route/community_route.dart' as community_routes;

import 'package:changqing_health_app/module/login/route/login_route.dart' as login_routes;
import 'package:changqing_health_app/module/home/<USER>/home_route.dart' as home_routes;
import 'package:changqing_health_app/module/agreement/web_page/routes/agreement_webview_routes.dart' as agreement_routes;
import 'package:changqing_health_app/module/user/route/user_route.dart' as user_routes;
// import 'package:changqing_health_app/module/liveroom/route/live_room_route.dart' as liveroom_routes;
import 'package:changqing_health_app/module/other/config/app_debug_config_page.dart' as app_debug_config_routes;
import 'package:changqing_health_app/module/course_table/route/course_table_route.dart' as course_table_routes;
import 'package:changqing_health_app/webview/route/common_web_route.dart' as common_web_routes;

String previousRouteName = '';
String currentRouteName = '';


// 不需要登录状态的页面
final _noLoginRoutes = [
  const root_routes.RootRoute().location,
  home_routes.HomeRoute().location,
  // community_routes.CommunityRoute().location,
  user_routes.UserRoute().location,
  const agreement_routes.AgreementWebviewRoute().location,
  const agreement_routes.PrivacyPolicyWebviewRoute().location,
  Uri.parse(video_routes.VideoRoute(videoUrl: '').location).path,
  user_routes.ReferenceLinkRoute().location,
  Uri.parse(community_routes.CommunityArticleRoute(id: 0).location).path,
  user_routes.ReferenceLinkArticleRoute().location,
];

/// 路由提供者
final routerProvider = Provider<GoRouter>((ref) {
  // ref.watch(loginProvider);
  return GoRouter(
    initialLocation: root_routes.RootRoute().location,
    routes: [
      ...root_routes.$appRoutes,
      ...navigator_routes.$appRoutes,
      ...video_routes.$appRoutes,
      ...community_routes.$appRoutes,
      ...login_routes.$appRoutes,
      ...agreement_routes.$appRoutes,
      ...user_routes.$appRoutes,
      // ...liveroom_routes.$appRoutes,
      ...app_debug_config_routes.$appRoutes,
      ...common_web_routes.$appRoutes,
      ...course_table_routes.$appRoutes,
    ],
    navigatorKey: navigatorKey,
    observers: [
      BotToastNavigatorObserver(),
      routeObserver,
    ],
    redirect: (context, state) {
      debugPrint('previousRouteName = $previousRouteName   currentRouteName = $currentRouteName');
      previousRouteName = currentRouteName;
      currentRouteName = state.uri.path;
      debugPrint('state = ${state.uri.path}');
      if (_noLoginRoutes.contains(state.uri.path)) {
        return null;
      }
      // 是否同意用户协议
      final isAgreeAgreement = SpUtil().get(spFirstInstallAgreement) ?? false;
      if (!isAgreeAgreement) {
        return root_routes.RootRoute().location;
      }
      final isLogined = ref.read(loginProvider);
      if (isLogined) return null;
      return ref.read(login_routes.getLoginRouteProvider);
    },
  );
});
