import 'package:changqing_health_app/constant/constant.dart';
import 'package:changqing_health_app/service/http/api_client.dart';
import 'package:changqing_health_app/service/http/request_interceptor.dart';
import 'package:changqing_health_app/service/http/response_model.dart';
import 'package:changqing_health_app/util/sp_util.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_provider.g.dart';

/// author: Liusilong
/// date: 2024/8/26
/// desc:
//

// dio 请求拦截器
final requestInterceptorProvider = Provider((ref) => RequestInterceptor(ref: ref));

// 是否同意隐私协议
final privacyAgreementProvider = StateProvider((ref) => SpUtil().get(spPrivacyAgreement) ?? false);

/// 版本号信息
final deviceInfoProvider = FutureProvider<PackageInfo>((ref) async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  return packageInfo;
});

// 问题：
// 当使用scheme拉起App时，route会定向到‘/’页面
// 需要一个provider来存储scheme的uri
// 在 routerProvider中的 redirect 中判断
// 如果有待处理的scheme，则本该定向到‘/’页面的逻辑，改为不处理，直接停留在当前页面
final schemeUriProvider = StateProvider<Uri?>((ref) => null);

// App后端配置
// http://api-doc.weimiaocaishang.com/project/1376/interface/api/101164
/*
flutter: onResponse = {code: 0, msg: OK, success: true, data: {replaceAliPayScheme: alipays, replaceWechatPayScheme: {extractRedirectDomain: r'redirect_url=([^:/]+)', extractRedirectUrl: r'(redirect_url=[^&]+)', startsWith: https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb}}}
 */
Map<String, dynamic> appBackendConfig = {};

@Riverpod(keepAlive: true)
class AppBackendConfig extends _$AppBackendConfig {
  @override
  FutureOr<Map<String, dynamic>> build() async {
    try {
      ResponseModel resModel = await ApiClient.get("/api/cqapp/app/common/app-config");
      if (resModel.code == 0) {
        appBackendConfig = resModel.data;
        try {
          servicePhone = appBackendConfig['tel'];
        } catch (_) {}
        return resModel.data;
      }
    } catch (_) {}
    return {};
  }
}
