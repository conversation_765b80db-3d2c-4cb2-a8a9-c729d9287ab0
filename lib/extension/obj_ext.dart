import 'package:flutter/material.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 2024/3/13
/// desc:
//
extension ObjExt on Object? {
  // 判断对象是否为 null
  bool get isBlank {
    if (this == null) return true;
    if (this is String) return (this as String).trim().isEmpty;
    if (this is List) return (this as List).isEmpty;
    if (this is Map) return (this as Map).isEmpty;
    return false;
  }
}