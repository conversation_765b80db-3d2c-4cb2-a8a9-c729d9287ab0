import 'package:flutter_riverpod/flutter_riverpod.dart';

extension RefreshFutureExtension on Ref {
  Future<T> refreshAsyncFuture<Notifier extends AutoDisposeAsyncNotifier<T>, T>(
    AutoDisposeAsyncNotifierProvider<Notifier, T> provider,
  ) {
    invalidate(provider);
    return read(provider.future);
  }

  Future<T> refreshFuture<T>(AutoDisposeFutureProvider<T> provider) {
    invalidate(provider);
    return read(provider.future);
  }
}
