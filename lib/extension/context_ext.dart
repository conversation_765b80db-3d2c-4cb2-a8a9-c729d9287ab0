import 'package:changqing_health_app/widget/modal_bottom_sheet_widget.dart';
import 'package:flutter/material.dart';

/// author: Liu<PERSON><PERSON>
/// date: 2024/2/28
/// desc:
//

extension ContextExt on BuildContext {
  /// 底部弹窗
  /// [child] child
  /// [sheetTitle] sheet 标题
  Future<T?> showBottomSheet<T>({
    required Widget child,
    String? sheetTitle,
    double? minHeight,
    double? maxHeight,
    bool useSafeArea = true,
    bool isScrollControlled = true,
  }) {
    return showModalBottomSheet(
      context: this,
      isScrollControlled: isScrollControlled,
      useSafeArea: useSafeArea,
      backgroundColor: Colors.transparent,
      routeSettings: RouteSettings(name: child.runtimeType.toString()),
      builder: (context) => ModalBottomSheetWidget(
        title: sheetTitle,
        minHeight: minHeight,
        maxHeight: maxHeight,
        child: child,
      ),
    );
  }

  /// 底部弹窗
  /// [child] sheet 中的 widget
  /// [minHeight] 底部弹出框最小高度
  /// [maxHeight] 底部弹出框最大高度
  /// [useSafeArea]
  ///   true: 底部弹出框高度最高位为安全区域高度;
  ///   false: 底部弹出框高度最高位为屏幕高度(撑满全屏)
  /// [isScrollControlled]
  ///   ture: 允许底部弹出框随内容高度调整（可全屏高度）;
  ///   false: 底部弹出框高度最大为默认高度，[maxHeight] 在大于默认高度的情况下不会生效
  /// [enableInput] 如果 [child] 中有输入框，该属性需要设置为 true，这种情况下，不能设置 [maxHeight]，否则会导致键盘遮挡输入框
  /// 注意：如果 [child] 中有输入框，如 [TextField]，则需要使用 [SingleChildScrollView] 将 [child] 包裹，否则键盘会遮挡输入框
  Future<T?> showBottomSheetV2<T>({
    required Widget child,
    double? minHeight,
    double? maxHeight,
    bool useSafeArea = true,
    bool isScrollControlled = true,
    bool enableInput = false,
  }) {
    return showModalBottomSheet(
      context: Navigator.of(this, rootNavigator: true).context,
      isScrollControlled: isScrollControlled,
      useSafeArea: useSafeArea,
      backgroundColor: Colors.transparent,
      routeSettings: RouteSettings(name: child.runtimeType.toString()),
      builder: (context) {
        final Widget result;
        // child 中有输入框
        if (enableInput) {
          result = SingleChildScrollView(
            padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
            child: child,
          );
        } else {
          result = child;
        }
        return ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: enableInput ? sh : maxHeight ?? sh,
            minHeight: minHeight ?? 0,
            minWidth: sw,
            maxWidth: sw,
          ),
          child: result,
        );
      },
    );
  }

  /// show 自定义的 Dialog
  void showCustomDialog({required Widget child, bool barrierDismissible = true}) {
    showDialog(context: this, builder: (context) => child, barrierDismissible: barrierDismissible);
  }

  /// 获取屏幕宽度
  double get sw => MediaQuery.of(this).size.width;

  /// 获取屏幕高度
  double get sh => MediaQuery.of(this).size.height;

  /// 获取屏幕方向
  Orientation get orientation => MediaQuery.of(this).orientation;

  bool get portrait => orientation == Orientation.portrait;

  bool get landscape => orientation == Orientation.landscape;

  double get paddingTop => MediaQuery.of(this).padding.top;

  double get paddingBottom => MediaQuery.of(this).padding.bottom;

  double get paddingLeft => MediaQuery.of(this).padding.left;

  double get paddingRight => MediaQuery.of(this).padding.right;

  /// 是否是当前页面
  bool get isCurrent => ModalRoute.of(this)!.isCurrent;
}
