import 'package:changqing_health_app/app/application.dart';
import 'package:changqing_health_app/extension/obj_ext.dart';
import 'package:changqing_health_app/widget/keep_alive_widget.dart';
import 'package:flutter/material.dart';

/// author: <PERSON><PERSON><PERSON>
/// date: 4/22/21
/// desc: Widget 扩展
//
int _prev = 0;

extension WidgetExtension on Widget {
  /// 添加点击事件
  /// [onTap] 点击事件
  /// [tapInterval] 两次 tap 事件之间的间隔，防止暴力点击，默认 800ms
  Widget onTap(Function? onTap, {int tapInterval = 800}) {
    return GestureDetector(
      onTap: () {
        if (onTap != null) {
          int now = DateTime.now().millisecondsSinceEpoch;
          if (now - _prev > tapInterval) {
            onTap();
            _prev = now;
          }
        }
      },
      child: this,
    );
  }

  /// widget 仅在竖屏可见
  Widget visibleInPortrait() {
    Orientation orientation = MediaQuery.of(globalContext!).orientation;
    if (orientation.isBlank || orientation == Orientation.portrait) return this;
    return const SizedBox.shrink();
  }

  /// widget 仅在横屏可见
  Widget visibleInLandscape() {
    Orientation orientation = MediaQuery.of(globalContext!).orientation;
    if (orientation.isBlank || orientation == Orientation.landscape) return this;
    return const SizedBox.shrink();
  }

  Widget get addToSliver => SliverToBoxAdapter(child: this);

  Widget addSliverPadding(EdgeInsets padding) {
    return SliverPadding(padding: padding, sliver: this);
  }

  Widget addCenter() {
    return Center(child: this);
  }

  Widget get withKeepAlive {
    return KeepAliveWidget(child: this);
  }
}
