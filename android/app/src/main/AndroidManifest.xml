<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
                 android:maxSdkVersion="29" />
    <!-- Gal: For supporting API <= 29 :Gal-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
                 android:maxSdkVersion="29" />
    <!-- Gal: For supporting API <= 29 :Gal-->
    <application
        android:label="长轻营养⾷疗"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"> <!-- Gal: For saving to album in API 29 :Gal-->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"
            />

            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
                        <!-- 微信唤起 app start-->
            <intent-filter>
                <action android:name="${applicationId}.FlutterActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:host="${applicationId}"
                    android:path="/"
                    android:scheme="wechatextmsg" />
            </intent-filter>
            <!-- 微信唤起 app end-->

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:scheme="cqapp"
                    android:host="toLivePage" />
            </intent-filter>

        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <!-- 微信唤起 app start-->
        <meta-data
            android:name="weChatAppId"
            android:value="wx92e1f9209d62afe1" />
        <!-- 微信唤起 app end-->
        <!--禁用 Impeller-->
        <meta-data
            android:name="io.flutter.embedding.android.EnableImpeller"
            android:value="false" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>

     <!-- 微信唤起 app  兼容 android 11 start-->
    <queries>
        <intent>
            <action android:name="${applicationId}.FlutterActivity" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data
                android:host="${applicationId}"
                android:path="/"
                android:scheme="wechatextmsg" />
        </intent>
    </queries>
    <!-- 微信唤起 app 兼容 android 11 end-->
</manifest>