# changqing_health_app

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

flutter build ios --release -t ./lib/main_prod.dart
flutter build apk --release -t ./lib/main_prod.dart

dart run build_runner watch -d
dart run build_runner clean
dart run build_runner build
dart run build_runner build --delete-conflicting-outputs

adb shell am start -a android.intent.action.VIEW -d "cqapp://toLivePage?liveUrl=https%3A%2F%2Fwork-intranet.qiushaocloud.top%2Flive.html%3Flcn%3D1322713322_1"


https://test-live.cqslim.cn/?appToken=APP_TOKEN&appKey=APP_KEY&vconsole=wm#/live-classroom/10138566


https://test-live.cqslim.cn/?appToken=APP_TOKEN&appKey=APP_KEY&vconsole=wm#/live-classroom/10138566
adb shell am start -a android.intent.action.VIEW -d "cqapp://toLivePage?liveUrl=https%3A%2F%2Ftest-live.cqslim.cn%2F%3FappToken%3DAPP_TOKEN%26appKey%3DAPP_KEY%26vconsole%3Dwm%23%2Flive-classroom%2F10138566"

https://test-course.99shiliao.cn/?appToken=APP_TOKEN&appKey=APP_KEY#/live-reserve?id=1681


大课
https://test-dk-live.dietcq.cn/?appToken=APP_TOKEN&appKey=APP_KEY&vconsole=wm#/live-classroom/10138324
adb shell am start -a android.intent.action.VIEW -d "cqapp://toLivePage?liveUrl=https%3a%2f%2ftest-dk-live.dietcq.cn%2f%3fappToken%3dAPP_TOKEN%26appKey%3dAPP_KEY%26vconsole%3dwm%23%2flive-classroom%2f10138324"



厨房
https://test-jkcf-live.dietcq.cn/?appToken=APP_TOKEN&appKey=APP_KEY&vconsole=wm#/live-classroom/10138883
adb shell am start -a android.intent.action.VIEW -d "cqapp://toLivePage?liveUrl=https%3a%2f%2ftest-jkcf-live.dietcq.cn%2f%3fappToken%3dAPP_TOKEN%26appKey%3dAPP_KEY%26vconsole%3dwm%23%2flive-classroom%2f10138883"


验证 图片转外链
https://test-jkcf-live.changqingdaojia.net/?appToken=APP_TOKEN&appKey=APP_KEY&vconsole=wm#/live-classroom/10139041
adb shell am start -a android.intent.action.VIEW -d "cqapp://toLivePage?liveUrl=https%3a%2f%2ftest-jkcf-live.changqingdaojia.net%2f%3fappToken%3dAPP_TOKEN%26appKey%3dAPP_KEY%26vconsole%3dwm%23%2flive-classroom%2f10139041"


