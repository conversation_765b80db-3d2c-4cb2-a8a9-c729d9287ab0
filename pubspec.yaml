name: chang<PERSON>_health_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.7+10700

environment:
  sdk: ^3.6.0

fluwx:
  ios:
    no_pay: true

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.

dependency_overrides:
  intl: 0.19.0
  webview_flutter_android:
    git:
      url: http://gitlab.weimiaocaishang.com/front/wm_webview_flutter_android
      ref: 3f2d94a1788a6f86a929cb6c7d12d3ef101ce1c1

  webview_flutter_wkwebview:
    git:
      url: http://gitlab.weimiaocaishang.com/front/**********************
      ref: eda2952cf05b71a7e155ec4f2eec48ea8c0b71d6
  # cq_flutter_gal:
  #   path: /Users/<USER>/Liusilong/Dev/Flutter/plugins/gal-2.3.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  dio: ^5.4.1
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.1.6
  shared_preferences: ^2.2.2
  flutter_screenutil: ^5.9.0
  connectivity_plus: ^6.0.5
  webview_flutter: 4.10.0
  bot_toast: ^4.1.3
  sentry_flutter: 8.14.2
  #  分页器
  infinite_scroll_pagination: ^4.0.0
  gap: ^3.0.1
  freezed_annotation: ^2.4.4
  go_router: 14.8.1
  json_annotation: ^4.9.0
  # flutter_gemini: ^3.0.0
  flutter_markdown: ^0.7.6+2
  uuid: ^4.5.1
  flutter_svg: ^2.0.17
  flutter_animate: ^4.5.2
  cached_network_image: ^3.4.1
  url_launcher: ^6.3.1
  # video_thumbnail: ^0.5.3
  # get_thumbnail_video: ^0.7.3
  get_video_thumbnail: ^0.7.0
  path_provider: ^2.1.5
  markdown: ^7.1.1
  # image_gallery_saver_plus: ^4.0.0
  permission_handler: ^11.4.0
  photo_view: ^0.15.0
  # pull_to_refresh_flutter3: ^2.0.2
  # media_kit: ^1.1.11
  # media_kit_video: ^1.2.5
  # media_kit_libs_video: ^1.0.5
  device_info_plus: 11.1.1
  # gal: ^2.3.1
  image_picker: ^1.1.2
  file_picker: ^9.2.1
  # extended_image: 9.1.0
  dismissible_page: ^1.0.2
  flutter_keyboard_visibility: ^6.0.0
  pull_down_button: ^0.10.2
  flutter_exit_app: ^1.1.4
  flutter_html: ^3.0.0
#  cq_flutter_ai_chat:
#    git:
#      url: http://gitlab.weimiaocaishang.com/front/cq_flutter_ai_chat.git
#      ref: feat_think-scroll

  wm_ali_player:
    git:
      url: http://gitlab.weimiaocaishang.com/front/flutter_wm_ali_player.git
      ref: 642c9313b1e178b874692d3cd35682100baaa87f

#  cq_flutter_live_room:
#    git:
#      url: http://gitlab.weimiaocaishang.com/front/cq_flutter_live_room.git
#      ref: xiufuying

  fluwx: 5.5.0

  lottie: 3.3.1

  encrypt: 5.0.1
  crypto: 3.0.3

  package_info_plus: ^8.3.0

  gal:
    git:
      url: http://gitlab.weimiaocaishang.com/front/cq_flutter_gal.git
      ref: main
  intl: ^0.20.2

  contained_tab_bar_view: 0.8.0

  flutter_native_splash: 2.4.4
  cq_youzan_webview:
    git: 
      url: http://gitlab.weimiaocaishang.com/front/cq_youzan_webview.git

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.6
  riverpod_generator: ^2.3.3
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  go_router_builder: 2.8.2

  app_links: 6.4.0

  pull_to_refresh: 2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/tab/
    - assets/images/loading/
    - assets/images/login/
    - assets/images/my_center/
    - assets/images/study/
    - assets/images/course/
  fonts:
    - family: AliPayNumber
      fonts:
        - asset: assets/fonts/AliPayNumber.ttf

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
