#import "AppDelegate.h"
#import "GeneratedPluginRegistrant.h"

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
    didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    [self addWindowObserver];
  [GeneratedPluginRegistrant registerWithRegistry:self];
  // Override point for customization after application launch.
  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}
//
//-(BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
//    
//    return YES;
//}
//
//-(BOOL)application:(UIApplication *)application handleOpenURL:(NSURL *)url {
//    return YES;
//}
//
//-(BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation {
//    return YES;
//}


-(void)addWindowObserver {
    // 注册监听
        [[NSNotificationCenter defaultCenter] addObserverForName:UIWindowDidBecomeVisibleNotification
                                                          object:nil
                                                           queue:[NSOperationQueue mainQueue]
                                                      usingBlock:^(NSNotification * _Nonnull note) {
            
    
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                NSLog(@"UIWindowDidBecomeVisibleNotification");
                UIViewController *vc = [self topViewController];
                NSString *className = NSStringFromClass([vc class]);
                NSLog(@"className = %@", className);
                if ([className containsString:@"AVFullScreen"]) {
                    NSLog(@"✅ 进入系统视频播放器，强制横屏");
                    [self setOrientation:UIInterfaceOrientationMaskLandscapeRight];
                }
            });
        
        }];

        [[NSNotificationCenter defaultCenter] addObserverForName:UIWindowDidBecomeHiddenNotification
                                                          object:nil
                                                           queue:[NSOperationQueue mainQueue]
                                                      usingBlock:^(NSNotification * _Nonnull note) {
       
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                NSLog(@"UIWindowDidBecomeHiddenNotification");
            
                UIViewController *vc = [self topViewController];
                NSString *className = NSStringFromClass([vc class]);
                NSLog(@"className = %@", className);
                if ([className containsString:@"AVFullScreen"]) {
                    NSLog(@"↩️ 退出系统视频播放器，恢复竖屏");
                    [self setOrientation:UIInterfaceOrientationMaskPortrait];
                }
            });
            
        }];
}


- (UIInterfaceOrientationMask)application:(UIApplication *)application supportedInterfaceOrientationsForWindow:(UIWindow *)window {
    
    UIViewController *topVC = [self topViewController];

    NSLog(@"[NSStringFromClass([topVC class]) = %@", NSStringFromClass([topVC class]));
    // 判断是否是播放视频的“全屏窗口”
    if ([NSStringFromClass([topVC class]) containsString:@"AVFullScreen"]) {
        return UIInterfaceOrientationMaskAllButUpsideDown; // 支持横屏
    }else {
        NSLog(@"↩️ 退出系统视频播放器，恢复竖屏");
        [self setOrientation:UIInterfaceOrientationMaskPortrait];
    }

    // 默认竖屏
    return UIInterfaceOrientationMaskAllButUpsideDown; // 支持横屏
}



- (UIViewController *)topViewController {
    // 遍历所有 window，找到最上层可见的
    NSArray<UIWindow *> *windows = UIApplication.sharedApplication.windows;
    for (UIWindow *window in windows.reverseObjectEnumerator) {
        if (window.isKeyWindow && window.rootViewController) {
            UIViewController *rootVC = window.rootViewController;
            return [self findTopVCFrom:rootVC];
        }
    }
    return nil;
}

- (UIViewController *)findTopVCFrom:(UIViewController *)vc {
    while (vc.presentedViewController) {
        vc = vc.presentedViewController;
    }
    if ([vc isKindOfClass:[UINavigationController class]]) {
        return [self findTopVCFrom:((UINavigationController *)vc).topViewController];
    }
    if ([vc isKindOfClass:[UITabBarController class]]) {
        return [self findTopVCFrom:((UITabBarController *)vc).selectedViewController];
    }
    return vc;
}



- (void)setOrientation:(UIInterfaceOrientationMask)orientationMask {
    if (@available(iOS 16.0, *)) {
        UIWindowScene *windowScene = (UIWindowScene *)UIApplication.sharedApplication.connectedScenes.anyObject;
        UIWindowSceneGeometryPreferencesIOS *prefs = [[UIWindowSceneGeometryPreferencesIOS alloc] initWithInterfaceOrientations:orientationMask];
        [windowScene requestGeometryUpdateWithPreferences:prefs errorHandler:^(NSError * _Nonnull error) {
            NSLog(@"❌ 强制旋转失败: %@", error);
        }];
    } else {
        UIInterfaceOrientation orientation =
            orientationMask == UIInterfaceOrientationMaskPortrait ? UIInterfaceOrientationPortrait : UIInterfaceOrientationLandscapeRight;

        [[UIDevice currentDevice] setValue:@(orientation) forKey:@"orientation"];
        [UIViewController attemptRotationToDeviceOrientation];
    }
}

@end

